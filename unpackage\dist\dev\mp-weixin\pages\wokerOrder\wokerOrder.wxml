<view class="wrap data-v-0ec3f686"><u-popup vue-id="6c894980-1" show="{{showFinishPopup}}" mode="bottom" closeable="{{true}}" data-event-opts="{{[['^close',[['e0']]]]}}" bind:close="__e" class="data-v-0ec3f686" bind:__l="__l" vue-slots="{{['default']}}"><view class="finish-popup data-v-0ec3f686"><view class="popup-title data-v-0ec3f686">完成工单</view><view class="popup-content data-v-0ec3f686"><u-form vue-id="{{('6c894980-2')+','+('6c894980-1')}}" labelPosition="top" labelWidth="auto" model="{{finishForm}}" data-ref="finishForm" class="data-v-0ec3f686 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('6c894980-3')+','+('6c894980-2')}}" borderBottom="{{true}}" labelPosition="left" label="完成时间" required="{{true}}" class="data-v-0ec3f686" bind:__l="__l" vue-slots="{{['default']}}"><view style="display:flex;justify-content:flex-end;align-items:center;" class="data-v-0ec3f686"><text data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" bindtap="__e" class="data-v-0ec3f686">{{$root.m0+''}}<u-icon vue-id="{{('6c894980-4')+','+('6c894980-3')}}" label="uView" size="40" name="arrow-right" class="data-v-0ec3f686" bind:__l="__l"></u-icon></text></view><u-datetime-picker vue-id="{{('6c894980-5')+','+('6c894980-3')}}" show="{{showFinishTimePicker}}" mode="datetime" visibleItemCount="{{5}}" value="{{finishForm.finishTime}}" data-event-opts="{{[['^cancel',[['e2']]],['^confirm',[['e3']]],['^input',[['__set_model',['$0','finishTime','$event',[]],['finishForm']]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-0ec3f686" bind:__l="__l"></u-datetime-picker></u-form-item><u-form-item vue-id="{{('6c894980-6')+','+('6c894980-2')}}" borderBottom="{{true}}" label="备注" class="data-v-0ec3f686" bind:__l="__l" vue-slots="{{['default']}}"><u-textarea bind:input="__e" vue-id="{{('6c894980-7')+','+('6c894980-6')}}" border="none" placeholder="请输入备注信息" value="{{finishForm.remark}}" data-event-opts="{{[['^input',[['__set_model',['$0','remark','$event',[]],['finishForm']]]]]}}" class="data-v-0ec3f686" bind:__l="__l"></u-textarea></u-form-item><u-form-item vue-id="{{('6c894980-8')+','+('6c894980-2')}}" borderBottom="{{true}}" label="上传图片/视频" class="data-v-0ec3f686" bind:__l="__l" vue-slots="{{['default']}}"><uv-upload vue-id="{{('6c894980-9')+','+('6c894980-8')}}" accept="media" fileList="{{finishForm.fileList}}" multiple="{{true}}" maxCount="{{9}}" data-event-opts="{{[['^clickPreview',[['handleClickPreview']]],['^afterRead',[['afterRead']]],['^delete',[['handleDelete']]]]}}" bind:clickPreview="__e" bind:afterRead="__e" bind:delete="__e" class="data-v-0ec3f686" bind:__l="__l"></uv-upload></u-form-item></u-form></view><view class="popup-footer data-v-0ec3f686"><u-button vue-id="{{('6c894980-10')+','+('6c894980-1')}}" type="primary" data-event-opts="{{[['^click',[['submitFinish']]]]}}" bind:click="__e" class="data-v-0ec3f686" bind:__l="__l" vue-slots="{{['default']}}">提交</u-button></view></view></u-popup><u-modal vue-id="6c894980-11" show="{{modalShow}}" title="确认接单" content="确认接单吗？" showCancelButton="{{true}}" asyncClose="{{true}}" data-ref="uModal" data-event-opts="{{[['^confirm',[['confirm']]],['^cancel',[['e4']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-0ec3f686 vue-ref" bind:__l="__l"></u-modal><view class="search-box data-v-0ec3f686"><u-search vue-id="6c894980-12" shape="square" placeholder="工单名称" showAction="{{false}}" value="{{keyWords}}" data-event-opts="{{[['^search',[['handleSearch']]],['^input',[['__set_model',['','keyWords','$event',[]]]]]]}}" bind:search="__e" bind:input="__e" class="data-v-0ec3f686" bind:__l="__l"></u-search></view><view style="background-color:#fff;margin-top:10rpx;" class="data-v-0ec3f686"><u-subsection vue-id="6c894980-13" list="{{subList}}" keyName="label" current="{{currentStatus}}" data-event-opts="{{[['^change',[['sectionChange']]]]}}" bind:change="__e" class="data-v-0ec3f686" bind:__l="__l"></u-subsection></view><view class="content_box data-v-0ec3f686"><u-list vue-id="6c894980-14" height="100%" data-event-opts="{{[['^scrolltolower',[['scrolltolower']]]]}}" bind:scrolltolower="__e" class="data-v-0ec3f686" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><u-list-item vue-id="{{('6c894980-15-'+index)+','+('6c894980-14')}}" class="data-v-0ec3f686" bind:__l="__l" vue-slots="{{['default']}}"><uni-card vue-id="{{('6c894980-16-'+index)+','+('6c894980-15-'+index)}}" border="{{false}}" margin="0 0 20rpx 0" is-shadow="{{false}}" data-event-opts="{{[['^click',[['toDetail',['$0'],[[['list','id',item.$orig.id]]]]]]]}}" catch:click="__e" class="data-v-0ec3f686" bind:__l="__l" vue-slots="{{['default','title','actions']}}"><view style="padding:10rpx 0 10rpx 0;border-bottom:2rpx solid #eee;display:flex;justify-content:space-between;align-items:center;" class="data-v-0ec3f686" slot="title"><view class="data-v-0ec3f686"><text style="font-weight:bolder;" class="data-v-0ec3f686">{{item.$orig.objectName||"--"}}</text></view><view style="min-width:120rpx;display:flex;align-items:center;gap:10rpx;" class="data-v-0ec3f686"><uni-tag vue-id="{{('6c894980-17-'+index)+','+('6c894980-16-'+index)}}" type="primary" inverted="{{true}}" text="{{item.g0.label}}" class="data-v-0ec3f686" bind:__l="__l"></uni-tag><uni-tag vue-id="{{('6c894980-18-'+index)+','+('6c894980-16-'+index)}}" type="primary" text="{{item.g1.label}}" class="data-v-0ec3f686" bind:__l="__l"></uni-tag></view></view><block><view class="card-actions data-v-0ec3f686" slot="actions"><view style="display:flex;justify-content:space-around;width:100%;align-items:center;" class="data-v-0ec3f686"><u--text vue-id="{{('6c894980-19-'+index)+','+('6c894980-16-'+index)}}" size="12" text="{{item.$orig.objectStatus==3?'工单待开始':item.$orig.objectStatus==1?'工单进行中':'工单已完成'}}" type="primary" class="data-v-0ec3f686" bind:__l="__l"></u--text><view class="data-v-0ec3f686"><block wx:if="{{item.$orig.objectStatus==3}}"><u-button vue-id="{{('6c894980-20-'+index)+','+('6c894980-16-'+index)}}" text="开始" size="mini" type="primary" data-event-opts="{{[['^tap',[['handleStart',['$0'],[[['list','id',item.$orig.id]]]]]]]}}" catch:tap="__e" class="data-v-0ec3f686" bind:__l="__l"></u-button></block><block wx:if="{{item.$orig.objectStatus==1}}"><u-button vue-id="{{('6c894980-21-'+index)+','+('6c894980-16-'+index)}}" text="完成" size="mini" type="primary" data-event-opts="{{[['^tap',[['handleFinish',['$0'],[[['list','id',item.$orig.id]]]]]]]}}" catch:tap="__e" class="data-v-0ec3f686" bind:__l="__l"></u-button></block><block wx:if="{{item.$orig.objectStatus==2&&item.$orig.payStatus==null}}"><u-button vue-id="{{('6c894980-22-'+index)+','+('6c894980-16-'+index)}}" text="申请结算" size="mini" type="primary" data-event-opts="{{[['^tap',[['handleSettled',['$0'],[[['list','id',item.$orig.id]]]]]]]}}" catch:tap="__e" class="data-v-0ec3f686" bind:__l="__l"></u-button></block></view></view></view></block><view class="data-v-0ec3f686"><view class="item data-v-0ec3f686"><text style="font-size:28rpx;color:#555;" class="data-v-0ec3f686">服务客户：</text><text style="color:#000;" class="data-v-0ec3f686">{{item.$orig.finalCustomer}}</text></view><view style="display:flex;justify-content:space-between;width:100%;" class="data-v-0ec3f686"><view class="item data-v-0ec3f686" style="width:50%;"><text style="font-size:28rpx;color:#555;" class="data-v-0ec3f686">联系人：</text><text style="color:#000;" class="data-v-0ec3f686">{{item.$orig.contact}}</text></view><view class="item data-v-0ec3f686" style="width:50%;"><text style="font-size:28rpx;color:#555;" class="data-v-0ec3f686">联系电话：</text><text style="color:#000;" class="data-v-0ec3f686">{{item.$orig.contactPhone||""}}</text></view></view><view class="item data-v-0ec3f686"><view style="font-size:28rpx;color:#555;" class="data-v-0ec3f686">服务地址：</view><text style="color:#000;" class="data-v-0ec3f686">{{item.$orig.distributionAddress}}</text></view><view class="item data-v-0ec3f686"><text style="font-size:28rpx;color:#555;" class="data-v-0ec3f686">服务时间：</text><text style="color:#000;" class="data-v-0ec3f686">{{item.$orig.planTime}}</text></view><view class="item data-v-0ec3f686"><view style="font-size:28rpx;color:#555;" class="data-v-0ec3f686">工单内容：</view><text style="color:#000;" class="data-v-0ec3f686">{{item.$orig.remark}}</text></view></view></uni-card></u-list-item></block><uni-load-more vue-id="{{('6c894980-23')+','+('6c894980-14')}}" status="{{$root.g2==page.total?'noMore':'loading'}}" class="data-v-0ec3f686" bind:__l="__l"></uni-load-more></u-list></view><u-modal vue-id="6c894980-24" show="{{show}}" content="你还未完善手机号,姓名等信息,请点击确认去完善" data-event-opts="{{[['^confirm',[['toUserInfo']]]]}}" bind:confirm="__e" class="data-v-0ec3f686" bind:__l="__l"></u-modal><u-popup vue-id="6c894980-25" show="{{showSettlePopup}}" mode="bottom" closeable="{{true}}" data-event-opts="{{[['^close',[['e5']]]]}}" bind:close="__e" class="data-v-0ec3f686" bind:__l="__l" vue-slots="{{['default']}}"><view class="finish-popup data-v-0ec3f686"><view class="popup-title data-v-0ec3f686">申请结算</view><view class="popup-content data-v-0ec3f686"><u-form vue-id="{{('6c894980-26')+','+('6c894980-25')}}" labelPosition="top" labelWidth="auto" model="{{settleForm}}" data-ref="settleForm" class="data-v-0ec3f686 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('6c894980-27')+','+('6c894980-26')}}" borderBottom="{{true}}" label="结算金额" class="data-v-0ec3f686" bind:__l="__l" vue-slots="{{['default']}}"><u--input bind:input="__e" vue-id="{{('6c894980-28')+','+('6c894980-27')}}" placeholder="请输入结算金额" border="none" type="number" value="{{settleForm.totalPrice}}" data-event-opts="{{[['^input',[['__set_model',['$0','totalPrice','$event',[]],['settleForm']]]]]}}" class="data-v-0ec3f686" bind:__l="__l"></u--input></u-form-item><u-form-item vue-id="{{('6c894980-29')+','+('6c894980-26')}}" borderBottom="{{true}}" label="备注" class="data-v-0ec3f686" bind:__l="__l" vue-slots="{{['default']}}"><u-textarea bind:input="__e" vue-id="{{('6c894980-30')+','+('6c894980-29')}}" border="none" placeholder="请输入备注信息" value="{{settleForm.applyContent}}" data-event-opts="{{[['^input',[['__set_model',['$0','applyContent','$event',[]],['settleForm']]]]]}}" class="data-v-0ec3f686" bind:__l="__l"></u-textarea></u-form-item></u-form></view><view class="popup-footer data-v-0ec3f686"><u-button vue-id="{{('6c894980-31')+','+('6c894980-25')}}" type="primary" data-event-opts="{{[['^click',[['submitSettle']]]]}}" bind:click="__e" class="data-v-0ec3f686" bind:__l="__l" vue-slots="{{['default']}}">提交</u-button></view></view></u-popup></view>