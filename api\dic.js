import http from '@/http/api.js'

// 获取字典属性
const getdictListByUrl = (url) => {
	return http.request({
		url: url,
		method: 'get',

	})
}
// 获取字典属性通过Code
const getdictListByCode = (code) => {
	return http.request({
		url: '/blade-system/dict/dictionary?code=' + code,
		method: 'get',

	})
}
// 获取字典详情
const getdictDetail = (id) => {
	return http.request({
		url: '/blade-system/dict/detail',
		method: 'get',
		params: {
			id
		}

	})
}

export default {
	getdictListByUrl,
	getdictDetail,
	getdictListByCode
}