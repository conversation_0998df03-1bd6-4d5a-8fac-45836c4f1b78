<template>
  <page-meta
    :page-style="
      'overflow:' + (showFinishPopup || showSignDrawer ? 'hidden' : 'visible')
    "
  ></page-meta>
  <page-container
    :show="showFinishPopup || showSignDrawer"
    @beforeleave="
      showFinishPopup = false;
      showSignDrawer = false;
    "
  ></page-container>
  <view>
    <view class="order-detail">
      <!-- 服务客户信息 -->
      <view class="card shadow">
        <view class="card-header">
          <view class="header-icon">
            <uni-icons type="person" size="20" color="#000" />
          </view>
          <text class="card-title">服务客户信息</text>
        </view>
        <view class="card-content">
          <view class="info-row">
            <view class="info-item">
              <text class="label">客户名称</text>
              <text class="value">{{ detailForm.customerName }}</text>
            </view>
            <view class="info-item">
              <text class="label">联系人</text>
              <text class="value">{{ detailForm.contact }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="label">联系电话</text>
              <view style="display: flex; align-items: center"
                ><text class="value phone">{{ detailForm.contactPhone }}</text>
                <u-icon
                  name="phone"
                  color="#2979ff"
                  size="20"
                  @click="callPhone(detailForm.contactPhone)"
                  v-if="detailForm.contactPhone"
                ></u-icon
              ></view>
            </view>
            <view class="info-item">
              <text class="label">地址</text>
              <text class="value">{{ detailForm.distributionAddress }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 任务信息 -->
      <view class="card shadow">
        <view class="card-header">
          <view class="header-icon">
            <uni-icons type="compose" size="20" color="#000" />
          </view>
          <text class="card-title">任务信息</text>
        </view>
        <view class="card-content">
          <view class="info-row">
            <view class="info-item">
              <text class="label">任务名称</text>
              <text class="value tag">{{ detailForm.objectName }}</text>
            </view>
            <view class="info-item">
              <text class="label">服务类型</text>
              <text class="value tag">{{ detailForm.serverTypeName }}</text>
            </view>
          </view>

          <view class="info-row single">
            <view class="info-item full">
              <text class="label">服务时间</text>
              <text class="value time"
                >{{ detailForm.serviceStartTime }} -
                {{ detailForm.serviceEndTime }}</text
              >
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="label">发布人</text>
              <text class="value">{{ detailForm.projectLeaderName }}</text>
            </view>
            <view class="info-item full">
              <text class="label">指派工程师</text>
              <text class="value">{{ detailForm.handleUserName }}</text>
            </view>
          </view>

          <view class="info-row single">
            <view class="info-item full">
              <text class="label">SLA</text>
              <text class="value code">{{
                detailForm.SLATypeName || "-"
              }}</text>
            </view>
          </view>
          <view class="info-row single">
            <view class="info-item full">
              <text class="label">任务描述</text>
              <text class="value desc">{{ detailForm.taskDescription }}</text>
            </view>
          </view>
          <view class="info-row single">
            <view class="info-item full" v-for="item in detailForm.fileList">
              <text class="label">任务附件</text>
              <text
                class="value file-link"
                v-for="item in detailForm.fileList"
                >{{ item.originalName }}</text
              >
            </view>
          </view>
        </view>
      </view>

      <!-- 任务执行 -->
      <view class="card shadow">
        <view class="card-header">
          <view class="header-icon">
            <uni-icons type="calendar" size="20" color="#000" />
          </view>
          <text class="card-title">任务执行</text>
        </view>
        <view class="timeline-container">
          <view class="timeline">
            <view
              class="timeline-item"
              v-for="(item, index) in detailForm.milestoneVOList"
            >
              <view class="timeline-dot active"></view>
              <view class="timeline-line"></view>
              <view class="timeline-content">
                <view class="timeline-header">
                  <text class="timeline-title">{{ item.handleContent }}</text>
                  <text class="timeline-time">{{ item.createTime }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 完工报告 -->
      <view class="card shadow" v-if="detailForm.objectStatus == 2 || detailForm.objectStatus == 3">
        <view class="card-header">
          <view class="header-icon">
            <uni-icons type="checkmarkempty" size="20" color="#000" />
          </view>
          <text class="card-title">完工报告</text>
        </view>
        <view class="card-content">
          <view
            class="completion-report"
            v-for="(report, index) in detailForm.sealContractObjectResultVOList"
            :key="index"
          >
            <!-- 报告人员信息 -->
            <view class="report-header">
              <text class="report-title">{{ report.handleName || `工程师 ${index + 1}` }}</text>
              <text class="report-time">{{ report.serviceStartTime || '-' }}</text>
            </view>

            <!-- 签到信息折叠面板 -->
            <view class="collapsible-section" v-if="detailForm.isNeedSign == 1">
              <view
                class="section-header"
                @click="toggleSection('signin', index)"
              >
                <view class="section-title">
                  <uni-icons type="location" size="16" color="#2979ff" />
                  <text class="section-text">签到信息</text>
                </view>
                <view class="section-arrow" :class="{ 'expanded': getExpandedState('signin', index) }">
                  <uni-icons type="arrowdown" size="14" color="#666" />
                </view>
              </view>

              <view
                class="section-content"
                v-show="getExpandedState('signin', index)"
              >
                <view class="info-row">
                  <view class="info-item">
                    <text class="label">签到时间</text>
                    <text class="value time">{{ report.signTime || report.serviceStartTime || '-' }}</text>
                  </view>
                  <view class="info-item">
                    <text class="label">签到地址</text>
                    <text class="value">{{ report.signAddress || '现场签到' }}</text>
                  </view>
                </view>

                <!-- 签到图片 -->
                <view class="info-row single" v-if="report.signPhotoUrl && report.signPhotoUrl.length > 0">
                  <view class="info-item full">
                    <text class="label">签到图片</text>
                    <view class="photo-grid">
                      <view
                        class="photo-item"
                        v-for="(photo, photoIndex) in report.signPhotoUrl.split(',')"
                        :key="photoIndex"
                        @click="previewImage(photo, report.signPhotoUrl.split(',').map(item => ({link:item})))"
                      >
                        <image
                          :src="photo"
                          mode="aspectFill"
                          class="photo-image"
                        ></image>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 完成信息折叠面板 -->
            <view class="collapsible-section">
              <view
                class="section-header"
                @click="toggleSection('complete', index)"
              >
                <view class="section-title">
                  <uni-icons type="checkmarkempty" size="16" color="#4CAF50" />
                  <text class="section-text">完成信息</text>
                </view>
                <view class="section-arrow" :class="{ 'expanded': getExpandedState('complete', index) }">
                  <uni-icons type="arrowdown" size="14" color="#666" />
                </view>
              </view>

              <view
                class="section-content"
                v-show="getExpandedState('complete', index)"
              >
                <view class="info-row">
              <view class="info-item">
                <text class="label">服务开始时间</text>
                <text class="value time">{{
                  report.serviceStartTime || "-"
                }}</text>
              </view>
              <view class="info-item">
                <text class="label">服务结束时间</text>
                <text class="value time">{{
                  report.serviceEndTime || "-"
                }}</text>
              </view>
            </view>

            <view class="info-row">
              <view class="info-item">
                <text class="label">实际工时</text>
                <text class="value">{{ report.useTimes || "-" }}小时</text>
              </view>
              <view class="info-item">
                <text class="label">完成情况</text>
                <text
                  class="value tag"
                  
                  >{{ report.completeStatusName || "-" }}</text
                >
              </view>
            </view>

            <view class="info-row single" v-if="report.serviceReorder">
              <view class="info-item full">
                <text class="label">服务复盘</text>
                <text class="value desc">{{ report.serviceReorder }}</text>
              </view>
            </view>

            <view class="info-row single" v-if="report.completeRemark">
              <view class="info-item full">
                <text class="label">备注</text>
                <text class="value desc">{{ report.completeRemark }}</text>
              </view>
            </view>

            <!-- 现场图片 -->
            <view
              class="info-row single"
              v-if="report.workOrderPhotoList && report.workOrderPhotoList.length > 0"
            >
              <view class="info-item full">
                <text class="label">现场图片</text>
                <view class="photo-grid">
                  <view
                    class="photo-item"
                    v-for="(photo, photoIndex) in report.workOrderPhotoList"
                    :key="photoIndex"
                    @click="previewImage(photo.url, report.workOrderPhotoList)"
                  >
                    <image
                      :src="photo.link"
                      mode="aspectFill"
                      class="photo-image"
                    ></image>
                  </view>
                </view>
              </view>
            </view>

            <!-- 处理结果图片 -->
            <view
              class="info-row single"
              v-if="
                report.handleResultPhotoList &&
                report.handleResultPhotoList.length > 0
              "
            >
              <view class="info-item full">
                <text class="label">处理结果图片</text>
                <view class="photo-grid">
                  <view
                    class="photo-item"
                    v-for="(photo, photoIndex) in report.handleResultPhotoList"
                    :key="photoIndex"
                    @click="previewImage(photo.url, report.handleResultPhotoList)"
                  >
                    <image
                      :src="photo.link"
                      mode="aspectFill"
                      class="photo-image"
                    ></image>
                  </view>
                </view>
              </view>
            </view>

              </view>
            </view>

            <!-- 分隔线，如果不是最后一个报告 -->
            <view
              class="report-divider"
              v-if="index < detailForm.sealContractObjectResultVOList.length - 1"
            ></view>
          </view>
        </view>
      </view>

      <!-- 底部占位区域，防止内容被固定按钮遮挡 -->
      <view class="bottom-placeholder" v-if="showActionButtons"></view>
    </view>

    <!-- 固定在底部的操作区 -->
    <view class="fixed-action-bar" v-if="showActionButtons">
      <view class="action-buttons">
        <!-- 接单按钮 -->
        <template v-if="detailForm.objectStatus == 3">
          <u-button
            type="primary"
            class="action-btn primary"
            @tap="handleAccept"
            plain
            >接单</u-button
          >
        </template>

        <!-- 签到和完成按钮 -->
        <template v-else>
          <!-- 签到按钮 -->
          <u-button
            class="action-btn"
            type="primary"
            v-if="
              detailForm.objectStatus == 1 &&
              detailForm.isNeedSign == 1 &&
              detailForm.isSign == 0
            "
            icon="map"
            plain
            @tap="handleSign"
            >签到</u-button
          >

          <!-- 完成按钮 -->
          <u-button
            class="action-btn primary"
            type="primary"
            plain
            v-if="
              detailForm.isNeedSign == 1
                ? detailForm.isSign == 1
                : detailForm.objectStatus == 1
            "
            @tap="handleFinish"
            >完成</u-button
          >
        </template>
      </view>
    </view>

    <!-- 接单确认模态框 -->
    <u-modal
      :show="acceptOrderModalShow"
      @confirm="acceptOrderConfirm"
      ref="uModal"
      title="确认接单"
      content="确认接单吗？"
      showCancelButton
      @cancel="acceptOrderModalShow = false"
      :asyncClose="true"
    ></u-modal>

    <!-- 签到抽屉 -->
    <u-popup
      :show="showSignDrawer"
      mode="bottom"
      closeOnClickOverlay
      @close="showSignDrawer = false"
      :mask-click="true"
      background="#fff"
      style="z-index: 9999"
    >
      <view style="padding: 32rpx 24rpx">
        <view style="font-size: 32rpx; font-weight: bold; margin-bottom: 24rpx"
          >签到</view
        >
        <view style="margin-bottom: 24rpx">
          <view style="font-size: 28rpx; margin-bottom: 8rpx">选择位置</view>
          <u-button
            @tap="chooseLocation"
            type="primary"
            icon="map"
            :loading="locationLoading"
            loadingText="正在获取位置..."
            plain
          >
            {{ signAddress ? signAddress : "点击获取位置" }}
          </u-button>
        </view>
        <view style="margin-bottom: 24rpx">
          <view style="font-size: 28rpx; margin-bottom: 8rpx">上传照片</view>
          <uv-upload
            accept="media"
            @clickPreview="handleClickPreview"
            :fileList="signPhotoUrl"
            @afterRead="afterReadSign"
            @delete="handleDeleteSign"
            multiple
            :maxCount="9"
          >
          </uv-upload>
        </view>
        <u-button type="primary" @tap="submitSign">确认签到</u-button>
      </view>
    </u-popup>

    <!-- 完成任务弹窗 -->
    <u-popup
      :show="showFinishPopup"
      mode="bottom"
      @close="showFinishPopup = false"
      :closeable="true"
    >
      <view class="finish-popup">
        <view class="popup-title">完成工单</view>
        <view class="popup-content">
          <u-form
            labelPosition="top"
            labelWidth="auto"
            :model="finishForm"
            ref="finishForm"
          >
            <u-form-item
              borderBottom
              labelPosition="left"
              label="服务开始时间"
              required
            >
              <view
                style="
                  display: flex;
                  justify-content: flex-end;
                  align-items: center;
                "
              >
                <text @click="shiwServiceStartTimePicker = true"
                  >{{
                    dateFormat(
                      new Date(Number(finishForm.serviceStartTime)),
                      "yyyy-MM-dd hh:mm"
                    ) || "请选择服务开始时间"
                  }}
                  <u-icon label="uView" size="40" name="arrow-right"></u-icon
                ></text>
              </view>
              <u-datetime-picker
                v-model="finishForm.serviceStartTime"
                :show="shiwServiceStartTimePicker"
                @cancel="shiwServiceStartTimePicker = false"
                @confirm="shiwServiceStartTimePicker = false"
                mode="datetime"
                :visibleItemCount="5"
              ></u-datetime-picker>
            </u-form-item>
            <u-form-item
              borderBottom
              labelPosition="left"
              label="服务结束时间"
              required
            >
              <view
                style="
                  display: flex;
                  justify-content: flex-end;
                  align-items: center;
                "
              >
                <text @click="shiwServiceEndTimePicker = true"
                  >{{
                    dateFormat(
                      new Date(Number(finishForm.serviceEndTime)),
                      "yyyy-MM-dd hh:mm"
                    ) || "请选择服务结束时间"
                  }}
                  <u-icon label="uView" size="40" name="arrow-right"></u-icon
                ></text>
              </view>
              <u-datetime-picker
                v-model="finishForm.serviceEndTime"
                :show="shiwServiceEndTimePicker"
                @cancel="shiwServiceEndTimePicker = false"
                @confirm="shiwServiceEndTimePicker = false"
                mode="datetime"
                :visibleItemCount="5"
              ></u-datetime-picker>
            </u-form-item>
            <!-- 实际使用工时 -->
            <u-form-item labelPosition="left" label="实际使用工时">
              <u-input
                v-model="finishForm.useTimes"
                placeholder="请输入实际使用工时"
                :border="false"
                type="digit"
              ></u-input>
            </u-form-item>
            <u-form-item labelPosition="left" label="完成情况">
              <dicPicker
                dicUrl="/blade-system/dict-biz/dictionary?code=completeType"
                v-model="finishForm.completeStatus"
                placeholder="请选择完成情况"
              ></dicPicker>
            </u-form-item>
            <u-form-item borderBottom label="现场图">
              <uv-upload
                accept="media"
                @clickPreview="handleClickPreview"
                :fileList="finishForm.workOrderPhotos"
                @afterRead="afterReadForXC"
                @delete="handleDeleteForXC"
                multiple
                :maxCount="9"
              >
              </uv-upload>
            </u-form-item>
            <u-form-item borderBottom label="处理结果图">
              <uv-upload
                accept="media"
                @clickPreview="handleClickPreview"
                :fileList="finishForm.handleResultPhotos"
                @afterRead="afterReadForFinish"
                @delete="handleDeleteForFinish"
                multiple
                :maxCount="9"
              >
              </uv-upload>
            </u-form-item>
            <u-form-item borderBottom label="服务复盘">
              <u-textarea
                v-model="finishForm.serviceReorder"
                border="none"
                placeholder="请输入服务复盘"
              ></u-textarea>
            </u-form-item>
            <u-form-item borderBottom label="备注">
              <u-textarea
                v-model="finishForm.completeRemark"
                border="none"
                placeholder="请输入备注信息"
              ></u-textarea>
            </u-form-item>
          </u-form>
        </view>
        <view class="popup-footer">
          <u-button type="primary" @click="submitFinish">提交</u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import QQMapWX from "@/utils/qqmap-wx-jssdk.min.js";
import http from "../../http/api.js";
import { dateFormat } from "../../utils/date.js";
import dicPicker from "@/components/dic-picker/dic-picker.vue";

export default {
  name: "WokerOrderDetail",
  components: {
    dicPicker,
  },
  data() {
    return {
      detailForm: {
        // 添加模拟数据用于测试完工报告显示
        objectStatus: 2, // 已完成状态
        customerName: "测试客户公司",
        contact: "张经理",
        contactPhone: "13800138000",
        distributionAddress: "北京市朝阳区测试大厦10层",
        objectName: "服务器维护任务",
        serverTypeName: "硬件维护",
        serviceStartTime: "2024-01-15 09:00:00",
        serviceEndTime: "2024-01-15 18:00:00",
        projectLeaderName: "项目经理",
        handleUserName: "张工程师",
        SLATypeName: "标准SLA",
        taskDescription: "对服务器进行全面检查和维护，确保系统稳定运行。",
        fileList: [],
        milestoneVOList: [
          {
            handleContent: "任务已创建",
            createTime: "2024-01-14 10:00:00",
          },
          {
            handleContent: "工程师已接单",
            createTime: "2024-01-14 14:30:00",
          },
          {
            handleContent: "现场签到完成",
            createTime: "2024-01-15 09:00:00",
          },
          {
            handleContent: "任务已完成",
            createTime: "2024-01-15 18:00:00",
          },
        ],
        sealContractObjectResultVOList: [
          {
            handleName: "张工程师",
            serviceStartTime: "2024-01-15 09:00:00",
            serviceEndTime: "2024-01-15 17:30:00",
            useTimes: 8.5,
            completeStatus: 1,
            completeStatusName: "完全完成",
            signTime: "2024-01-15 08:55:00",
            signAddress: "北京市朝阳区测试大厦10层",
            serviceReorder: "本次服务顺利完成，客户设备运行正常。在服务过程中发现了一些潜在问题并及时处理，建议客户定期进行设备维护检查。",
            completeRemark: "设备已恢复正常运行，客户满意度较高。所有问题已解决，系统运行稳定。",
            signPhotoList: [
              { link: "https://via.placeholder.com/300x200/673AB7/white?text=签到图1" },
              { link: "https://via.placeholder.com/300x200/3F51B5/white?text=签到图2" }
            ],
            workOrderPhotoList: [
              { link: "https://via.placeholder.com/300x200/4CAF50/white?text=现场图1" },
              { link: "https://via.placeholder.com/300x200/2196F3/white?text=现场图2" },
              { link: "https://via.placeholder.com/300x200/FF9800/white?text=现场图3" }
            ],
            handleResultPhotoList: [
              { link: "https://via.placeholder.com/300x200/9C27B0/white?text=结果图1" },
              { link: "https://via.placeholder.com/300x200/F44336/white?text=结果图2" }
            ]
          },
          {
            handleName: "李技术员",
            serviceStartTime: "2024-01-16 14:00:00",
            serviceEndTime: "2024-01-16 16:00:00",
            useTimes: 2,
            completeStatus: 2,
            completeStatusName: "部分完成",
            signTime: "2024-01-16 13:55:00",
            signAddress: "北京市朝阳区测试大厦10层",
            serviceReorder: "协助主工程师完成设备调试工作，负责数据备份和系统配置。",
            completeRemark: "辅助工作完成良好，配合主工程师顺利完成任务。",
            signPhotoList: [
              { link: "https://via.placeholder.com/300x200/795548/white?text=李工签到" }
            ],
            workOrderPhotoList: [
              { link: "https://via.placeholder.com/300x200/607D8B/white?text=辅助现场图" }
            ],
            handleResultPhotoList: [
              { link: "https://via.placeholder.com/300x200/795548/white?text=辅助结果图" }
            ]
          }
        ]
      },
      dateFormat,
      report: {
        handleUserName: "张工程师",
        serviceStartTime: "2024-01-15 09:00:00",
        serviceEndTime: "2024-01-15 17:30:00",
        useTimes: 8.5,
        completeStatus: 1, // 完全完成
        serviceReorder:
          "本次服务顺利完成，客户设备运行正常。在服务过程中发现了一些潜在问题并及时处理，建议客户定期进行设备维护检查。具体完成内容包括：\n1. 服务器硬件检查\n2. 系统性能优化\n3. 安全补丁更新\n4. 数据备份验证",
        completeRemark:
          "设备已恢复正常运行，客户满意度较高。所有问题已解决，系统运行稳定。",
        workOrderPhotos: [
          {
            url: "https://via.placeholder.com/300x200/4CAF50/white?text=现场图1",
          },
          {
            url: "https://via.placeholder.com/300x200/2196F3/white?text=现场图2",
          },
          {
            url: "https://via.placeholder.com/300x200/FF9800/white?text=现场图3",
          },
        ],
        handleResultPhotos: [
          {
            url: "https://via.placeholder.com/300x200/9C27B0/white?text=结果图1",
          },
          {
            url: "https://via.placeholder.com/300x200/F44336/white?text=结果图2",
          },
        ],
      },

      // 签到相关
      showSignDrawer: false,
      locationLoading: false,
      signAddress: null,
      signPhotoUrl: [],
      signRemark: "",
      qqmapsdk: null,

      // 接单相关
      acceptOrderModalShow: false,

      // 完成相关
      showFinishPopup: false,
      shiwServiceStartTimePicker: false,
      shiwServiceEndTimePicker: false,
      finishForm: {
        serviceStartTime: Number(new Date()),
        serviceEndTime: Number(new Date()),
        completeRemark: "",
        useTimes: null,
        serviceReorder: "",
        workOrderPhotos: [],
        handleResultPhotos: [],
        completeStatus: null,
      },

      // 折叠面板状态
      expandedSections: {},
    };
  },
  computed: {
    // 是否显示操作按钮
    showActionButtons() {
      if (!this.detailForm.objectStatus) return false;

      // 显示条件：待接单 或 进行中
      return (
        this.detailForm.objectStatus == 3 || this.detailForm.objectStatus == 1
      );
    },
    sealContractObjectResultVOList() {
      return this.detailForm.sealContractObjectResultVOList || [];
    },
  },
  onLoad({ id }) {
    if (id) {
      this.getWorkerOrderDetail(id);
    }
    // 初始化地图实例
    this.qqmapsdk = new QQMapWX({
      key: "V37BZ-5JF63-HBZ3S-34XAJ-KPG2Q-74FPM", // 请替换为你的真实 Key
    });
  },

  methods: {
    getWorkerOrderDetail(id) {
      this.$u.api.getWorkerOrderDetail(id).then((res) => {
        this.detailForm = res.data;
        if (this.detailForm.completeFileList) {
          this.detailForm.completeFileList =
            this.detailForm.completeFileList.map((item) => {
              return {
                ...item,
                url: item.link,
              };
            });
        }

        console.log(this.detailForm);
      });
    },

    // 拨打电话
    callPhone(phone) {
      uni.makePhoneCall({
        phoneNumber: phone,
        success() {
          console.log("拨打电话成功！");
        },
        fail(err) {
          console.log("拨打电话失败！", err);
        },
      });
    },

    // 接单
    handleAccept() {
      this.acceptOrderModalShow = true;
    },

    acceptOrderConfirm() {
      this.$u.api
        .startWorkerOrder(this.detailForm.id)
        .then((res) => {
          this.acceptOrderModalShow = false;
          this.$u.toast("接单成功");
          // 重新获取详情
          this.getWorkerOrderDetail(this.detailForm.id);
        })
        .catch((err) => {
          this.$u.toast(err.message || "接单失败");
        });
    },

    // 签到
    handleSign() {
      this.showSignDrawer = true;
      this.signAddress = null;
      this.signPhotoUrl = [];
      this.signRemark = "";
      this.chooseLocation();
    },

    chooseLocation() {
      this.locationLoading = true;
      wx.getLocation({
        type: "gcj02",
        success: (res) => {
          console.log(res);
          // 成功获取经纬度后，进行逆地址解析
          this.qqmapsdk.reverseGeocoder({
            location: {
              latitude: res.latitude,
              longitude: res.longitude,
            },
            success: (result) => {
              // 逆解析成功回调
              console.log("逆地址解析结果：", result);
              const addressInfo = result.result.address_component;
              const formattedAddress = result.result.address;
              console.log("所在城市：", addressInfo.city);
              console.log("完整地址：", formattedAddress);
              this.locationLoading = false;
              this.signAddress = formattedAddress;
            },
            fail: (err) => {
              this.locationLoading = false;
              console.error("逆地址解析失败：", err);
              uni.showToast({ title: "位置解析失败", icon: "none" });
            },
          });
        },
        fail: (err) => {
          console.log(err);
          this.locationLoading = false;
          uni.showToast({ title: "位置选择失败", icon: "none" });
        },
      });
    },

    afterReadSign(event) {
      console.log(event);
      const { file } = event;
      const indexAll = this.signPhotoUrl.length;
      file.forEach((item, index) => {
        this.signPhotoUrl.push({
          ...item,
          status: "uploading",
          message: "上传中",
          index: indexAll + index,
        });
      });
      file.forEach((item, index) => {
        this.uploadFileSign(item.url, indexAll + index);
      });
    },

    uploadFileSign(url, index) {
      return new Promise((resolve) => {
        const params = {
          filePath: url,
          name: "file",
        };
        http.upload("/blade-resource/attach/upload", params).then((res) => {
          this.signPhotoUrl.find((item) => item.index == index).status =
            "success";
          this.signPhotoUrl.find((item) => item.index == index).message = "";
          this.signPhotoUrl.find((item) => item.index == index).url =
            res.data.link;
          resolve();
        });
      });
    },

    handleDeleteSign({ file, index, name }) {
      console.log(file, index, name);
      this.signPhotoUrl.splice(index, 1);
    },

    handleClickPreview(url, lists, name) {
      console.log(url, lists, name);
    },

    submitSign() {
      if (!this.signPhotoUrl || this.signPhotoUrl.length === 0) {
        uni.showToast({ title: "请上传照片", icon: "none" });
        return;
      }
      // 提交签到数据
      const data = {
        id: this.detailForm.id,
        address: this.signAddress,
        signPhotoUrl: this.signPhotoUrl.map((item) => item.url).join(","),
      };
      this.$u.api
        .signIn(data)
        .then((res) => {
          console.log(res);
          uni.showToast({ title: "签到成功", icon: "success" });
          this.showSignDrawer = false;
          // 重新获取详情
          this.getWorkerOrderDetail(this.detailForm.id);
        })
        .catch((err) => {
          this.$u.toast(err.message || "签到失败");
        });
    },

    // 完成任务
    handleFinish() {
      this.showFinishPopup = true;
      this.finishForm.serviceStartTime = Number(
        new Date(this.detailForm.serviceStartTime)
      );
      this.finishForm.serviceEndTime = Number(
        new Date(this.detailForm.serviceEndTime)
      );
    },

    afterReadForXC(event) {
      console.log(event);
      const { file } = event;
      const indexAll = this.finishForm.workOrderPhotos.length;
      file.forEach((item, index) => {
        this.finishForm.workOrderPhotos.push({
          ...item,
          status: "uploading",
          message: "上传中",
          index: indexAll + index,
        });
      });
      file.forEach((item, index) => {
        this.uploadFileForXC(item.url, indexAll + index);
      });
    },

    uploadFileForXC(url, index) {
      return new Promise((resolve) => {
        const params = {
          filePath: url,
          name: "file",
        };
        http.upload("/blade-resource/attach/upload", params).then((res) => {
          this.finishForm.workOrderPhotos.find(
            (item) => item.index == index
          ).status = "success";
          this.finishForm.workOrderPhotos.find(
            (item) => item.index == index
          ).message = "";
          this.finishForm.workOrderPhotos.find(
            (item) => item.index == index
          ).url = res.data.link;
          this.finishForm.workOrderPhotos.find(
            (item) => item.index == index
          ).id = res.data.id;
          resolve();
        });
      });
    },

    handleDeleteForXC({ file, index, name }) {
      console.log(file, index, name);
      this.finishForm.workOrderPhotos.splice(index, 1);
    },

    afterReadForFinish(event) {
      console.log(event);
      const { file } = event;
      const indexAll = this.finishForm.handleResultPhotos.length;
      file.forEach((item, index) => {
        this.finishForm.handleResultPhotos.push({
          ...item,
          status: "uploading",
          message: "上传中",
          index: indexAll + index,
        });
      });
      file.forEach((item, index) => {
        this.uploadFileForFinish(item.url, indexAll + index);
      });
    },

    uploadFileForFinish(url, index) {
      return new Promise((resolve) => {
        const params = {
          filePath: url,
          name: "file",
        };
        http.upload("/blade-resource/attach/upload", params).then((res) => {
          this.finishForm.handleResultPhotos.find(
            (item) => item.index == index
          ).status = "success";
          this.finishForm.handleResultPhotos.find(
            (item) => item.index == index
          ).message = "";
          this.finishForm.handleResultPhotos.find(
            (item) => item.index == index
          ).url = res.data.link;
          this.finishForm.handleResultPhotos.find(
            (item) => item.index == index
          ).id = res.data.id;
          resolve();
        });
      });
    },

    handleDeleteForFinish({ file, index, name }) {
      console.log(file, index, name);
      this.finishForm.handleResultPhotos.splice(index, 1);
    },

    submitFinish() {
      if (
        !this.finishForm.serviceStartTime ||
        !this.finishForm.serviceEndTime
      ) {
        this.$u.toast("开始时间和结束时间不能为空");
        return;
      }
      const formData = {
        id: this.detailForm.id,
        serviceStartTime: this.dateFormat(
          new Date(Number(this.finishForm.serviceStartTime)),
          "yyyy-MM-dd hh:mm:ss"
        ),
        serviceEndTime: this.dateFormat(
          new Date(Number(this.finishForm.serviceEndTime)),
          "yyyy-MM-dd hh:mm:ss"
        ),
        completeRemark: this.finishForm.completeRemark,
        handleResultPhotos:
          this.finishForm.handleResultPhotos &&
          this.finishForm.handleResultPhotos.map((item) => item.id).join(","),
        workOrderPhotos:
          this.finishForm.workOrderPhotos &&
          this.finishForm.workOrderPhotos.map((item) => item.id).join(","),
        useTimes: this.finishForm.useTimes,
        completeStatus: this.finishForm.completeStatus,
        serviceReorder: this.finishForm.serviceReorder,
      };
      this.$u.api
        .finishWorkerOrder(formData)
        .then((res) => {
          this.$u.toast("提交成功");
          this.showFinishPopup = false;
          this.finishForm = {
            serviceStartTime: Number(new Date()),
            serviceEndTime: Number(new Date()),
            completeRemark: "",
            useTimes: null,
            serviceReorder: "",
            workOrderPhotos: [],
            handleResultPhotos: [],
            completeStatus: null,
          };
          // 重新获取详情
          this.getWorkerOrderDetail(this.detailForm.id);
        })
        .catch((err) => {
          this.$u.toast(err.message || "提交失败");
        });
    },

    // 获取完成状态文本
    getCompleteStatusText(status) {
      const statusMap = {
        1: "完全完成",
        2: "部分完成",
        3: "未完成",
      };
      return statusMap[status] || "未知状态";
    },

    // 获取完成状态样式类
    getCompleteStatusClass(status) {
      const classMap = {
        1: "status-complete",
        2: "status-partial",
        3: "status-incomplete",
      };
      return classMap[status] || "";
    },

    // 预览图片
    previewImage(current, urls) {
      const imageUrls = urls.map((item) => item.link || item.url);
      uni.previewImage({
        current: current,
        urls: imageUrls,
      });
    },

    // 切换折叠面板状态
    toggleSection(type, index) {
      const key = `${type}_${index}`;
      this.$set(this.expandedSections, key, !this.expandedSections[key]);
    },

    // 获取折叠面板展开状态
    getExpandedState(type, index) {
      const key = `${type}_${index}`;
      // 默认展开完成信息，签到信息默认收起
      if (this.expandedSections[key] === undefined) {
        return type === 'complete';
      }
      return this.expandedSections[key];
    },
  },
};
</script>

<style scoped>
.order-detail {
  padding: 24rpx;
  background: linear-gradient(180deg, #f8faff 0%, #f0f4ff 100%);
  min-height: 100vh;
}

.card {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  border: 1rpx solid #e8f0fe;
  box-shadow: 0 8rpx 24rpx rgba(41, 121, 255, 0.08);
  transition: all 0.3s ease;
}

.shadow:hover {
  box-shadow: 0 12rpx 32rpx rgba(41, 121, 255, 0.12);
  transform: translateY(-2rpx);
}

.card-header {
  display: flex;
  align-items: center;
  padding: 28rpx 24rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #fff 100%);
  position: relative;
}

.card-header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.7) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
}

.header-icon {
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000;
  letter-spacing: 0.5rpx;
}

.card-content {
  padding: 32rpx 24rpx;
}

.info-row {
  display: flex;
  margin-bottom: 24rpx;
  gap: 24rpx;
}

.info-row.single {
  margin-bottom: 20rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-item.full {
  flex: 1;
}

.label {
  font-size: 24rpx;
  color: #8a8a8a;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
}

.value.important {
  color: #2979ff;
  font-weight: 600;
}

.value.tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  align-self: flex-start;
}

.value.time {
  color: #ff6b35;
  font-weight: 500;
}

.value.phone {
  color: #2979ff;
  text-decoration: underline;
}

.value.satisfy {
  color: #4caf50;
  font-weight: 600;
  background: #e8f5e8;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  align-self: flex-start;
}

.value.code {
  font-family: "Courier New", monospace;
  background: #f5f5f5;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.value.desc {
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #2979ff;
}

.value.file-link {
  color: #2979ff;
  text-decoration: underline;
  background: #e3f2fd;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  align-self: flex-start;
}

.timeline-container {
  padding: 16rpx;
}

.timeline {
  position: relative;
  padding-left: 48rpx;
}

.timeline-item {
  position: relative;
  padding-bottom: 32rpx;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -54rpx;
  top: 12rpx;
  width: 20rpx;
  height: 20rpx;
  background: #e0e0e0;
  border-radius: 50%;
  border: 4rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.timeline-dot.active {
  background: #2979ff;
  box-shadow: 0 0 0 4rpx rgba(41, 121, 255, 0.2);
}

.timeline-line {
  position: absolute;
  left: -46rpx;
  top: 36rpx;
  bottom: -32rpx;
  width: 4rpx;
  background: linear-gradient(180deg, #2979ff 0%, #e0e0e0 100%);
  border-radius: 2rpx;
}

.timeline-item:last-child .timeline-line {
  display: none;
}

.timeline-content {
  background: white;
  padding: 24rpx;
  border-radius: 16rpx;
  border: 1rpx solid #e8f0fe;
  box-shadow: 0 4rpx 12rpx rgba(41, 121, 255, 0.05);
  position: relative;
}

.timeline-content::before {
  content: "";
  position: absolute;
  left: -12rpx;
  top: 20rpx;
  width: 0;
  height: 0;
  border-top: 8rpx solid transparent;
  border-bottom: 8rpx solid transparent;
  border-right: 12rpx solid white;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.timeline-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2979ff;
}

.timeline-time {
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.timeline-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.timeline-operator {
  font-size: 24rpx;
  color: #999;
  font-style: italic;
}

/* 底部占位空间 */
.bottom-placeholder {
  height: 240rpx; /* 为固定底部操作栏留出空间 */
}

/* 固定底部操作栏 */
.fixed-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom)); /* 适配安全区域 */
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  border-top: 1rpx solid #f0f0f0;
  z-index: 999;
}

.action-buttons {
  display: flex;
  gap: 24rpx;
  justify-content: flex-end;
  align-items: center;
}

.action-btn {
  min-width: 120rpx;
  padding: 0 32rpx;
  height: 72rpx;
  line-height: 72rpx;
  border: none;
  border-radius: 36rpx;
  background: #f5f7fa;
  color: #2979ff;
  font-size: 28rpx;
  font-weight: 500;
  margin: 0;
  outline: none;
  box-shadow: 0 4rpx 12rpx 0 rgba(45, 140, 240, 0.15);
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(90deg, #2979ff 0%, #57a3f3 100%);
  color: #fff;
  box-shadow: 0 4rpx 16rpx 0 rgba(45, 140, 240, 0.3);
}

.action-btn:active {
  opacity: 0.85;
  transform: translateY(2rpx);
}

/* 完成弹窗样式 */
.finish-popup {
  height: 80vh;
  overflow-y: auto;
  position: relative;
}

.popup-title {
  padding-top: 20rpx;
  position: sticky;
  background-color: #fff;
  top: 0;
  z-index: 10;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
}

.popup-content {
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.popup-footer {
  padding: 20rpx 0;
  position: fixed;
  bottom: 0;
  left: 20rpx;
  right: 20rpx;
  width: auto;
}

/* 输入框右对齐 */
::v-deep input {
  text-align: right !important;
}

/* 完工报告样式 */
.completion-report {
  position: relative;
  margin-bottom: 32rpx;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, #f8faff 0%, #e3f2fd 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #2979ff;
}

.report-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2979ff;
}

.report-time {
  font-size: 24rpx;
  color: #666;
  background: rgba(255, 255, 255, 0.8);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

/* 折叠面板样式 */
.collapsible-section {
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 1rpx solid #e8f0fe;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8faff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.section-header:active {
  background: #e3f2fd;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.section-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.section-arrow {
  transition: transform 0.3s ease;
}

.section-arrow.expanded {
  transform: rotate(180deg);
}

.section-content {
  padding: 20rpx;
  background: #fff;
  border-top: 1rpx solid #e8f0fe;
}

.report-divider {
  height: 1rpx;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #e0e0e0 50%,
    transparent 100%
  );
  margin: 32rpx 0;
}

/* 完成状态标签样式 */
.value.tag.status-complete {
  background: #e8f5e8;
  color: #4caf50;
}

.value.tag.status-partial {
  background: #fff3e0;
  color: #ff9800;
}

.value.tag.status-incomplete {
  background: #ffebee;
  color: #f44336;
}

/* 图片网格样式 */
.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 12rpx;
}

.photo-item {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 1rpx solid #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.photo-item:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.photo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
