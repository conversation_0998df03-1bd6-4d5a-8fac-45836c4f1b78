{"version": 3, "sources": ["webpack:///D:/project/vt-unih5-order/uni_modules/uv-button/components/uv-button/uv-button.vue?bafc", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-button/components/uv-button/uv-button.vue?a1f5", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-button/components/uv-button/uv-button.vue?5a2b", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-button/components/uv-button/uv-button.vue?504c", "uni-app:///uni_modules/uv-button/components/uv-button/uv-button.vue", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-button/components/uv-button/uv-button.vue?df8f", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-button/components/uv-button/uv-button.vue?ef7c"], "names": ["name", "mixins", "emits", "data", "computed", "bemClass", "loadingColor", "iconColorCom", "baseColor", "style", "nvueTextStyle", "textSize", "size", "getIconSize", "btnWrapperStyle", "methods", "clickHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4WAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAAooB,CAAgB,mnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC0JxpB;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAxCA,eAyCA;EACAA;EAEAC;EAKAC;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACA,0BACA,2BACA;MACA;QACA;QACA,0BACA,mBACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACAC;QACA;UACA;UACAA;QACA;QACA;UACA;UACA;UACA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;QACA;UACA;UACAA;UACAA;UACAA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACAD;MACA;MACA;QACAA;MACA;MACAA;MACA;IACA;IACA;IACAE;MACA;QACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;QACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACpUA;AAAA;AAAA;AAAA;AAA+tC,CAAgB,0nCAAG,EAAC,C;;;;;;;;;;;ACAnvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uv-button/components/uv-button/uv-button.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uv-button.vue?vue&type=template&id=7439ab6c&scoped=true&\"\nvar renderjs\nimport script from \"./uv-button.vue?vue&type=script&lang=js&\"\nexport * from \"./uv-button.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uv-button.vue?vue&type=style&index=0&id=7439ab6c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7439ab6c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uv-button/components/uv-button/uv-button.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-button.vue?vue&type=template&id=7439ab6c&scoped=true&\"", "var components\ntry {\n  components = {\n    uvLoadingIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-loading-icon/components/uv-loading-icon/uv-loading-icon\" */ \"@/uni_modules/uv-loading-icon/components/uv-loading-icon/uv-loading-icon.vue\"\n      )\n    },\n    uvIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-icon/components/uv-icon/uv-icon\" */ \"@/uni_modules/uv-icon/components/uv-icon/uv-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.btnWrapperStyle])\n  var s1 = _vm.__get_style([_vm.baseColor, _vm.$uv.addStyle(_vm.customStyle)])\n  var m0 = Number(_vm.hoverStartTime)\n  var m1 = Number(_vm.hoverStayTime)\n  var s2 = _vm.loading\n    ? _vm.__get_style([\n        {\n          fontSize: _vm.textSize + \"px\",\n        },\n        _vm.$uv.addStyle(_vm.customTextStyle),\n      ])\n    : null\n  var s3 = !_vm.loading\n    ? _vm.__get_style([\n        {\n          fontSize: _vm.textSize + \"px\",\n        },\n        _vm.$uv.addStyle(_vm.customTextStyle),\n      ])\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        m0: m0,\n        m1: m1,\n        s2: s2,\n        s3: s3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-button.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-button.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view \r\n\t\tclass=\"uv-button-wrapper\"\r\n\t\t:style=\"[btnWrapperStyle]\"\r\n\t>\r\n    <!-- #ifndef APP-NVUE -->\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- 为了解决微信小程序动态设置hover-class点击态不消失的BUG -->\r\n\t\t<view class=\"uv-button-wrapper--dis\" v-if=\"disabled || loading\"></view>\r\n\t\t<button\r\n\t\t  :hover-start-time=\"Number(hoverStartTime)\"\r\n\t\t  :hover-stay-time=\"Number(hoverStayTime)\"\r\n\t\t  :form-type=\"formType\"\r\n\t\t  :open-type=\"openType\"\r\n\t\t  :app-parameter=\"appParameter\"\r\n\t\t  :hover-stop-propagation=\"hoverStopPropagation\"\r\n\t\t  :send-message-title=\"sendMessageTitle\"\r\n\t\t  :send-message-path=\"sendMessagePath\"\r\n\t\t  :lang=\"lang\"\r\n\t\t  :data-name=\"dataName\"\r\n\t\t  :session-from=\"sessionFrom\"\r\n\t\t  :send-message-img=\"sendMessageImg\"\r\n\t\t  :show-message-card=\"showMessageCard\"\r\n\t\t  @getphonenumber=\"onGetPhoneNumber\"\r\n\t\t  @getuserinfo=\"onGetUserInfo\"\r\n\t\t  @error=\"onError\"\r\n\t\t  @opensetting=\"onOpenSetting\"\r\n\t\t  @launchapp=\"onLaunchApp\"\r\n\t\t\t@contact=\"onContact\"\r\n\t\t\t@chooseavatar=\"onChooseavatar\"\r\n\t\t\t@agreeprivacyauthorization=\"onAgreeprivacyauthorization\"\r\n\t\t\t@addgroupapp=\"onAddgroupapp\"\r\n\t\t\t@chooseaddress=\"onChooseaddress\"\r\n\t\t\t@subscribe=\"onSubscribe\"\r\n\t\t\t@login=\"onLogin\"\r\n\t\t\t@im=\"onIm\"\r\n\t\t  hover-class=\"uv-button--active\"\r\n\t\t  class=\"uv-button uv-reset-button\"\r\n\t\t  :style=\"[baseColor, $uv.addStyle(customStyle)]\"\r\n\t\t  @tap=\"clickHandler\"\r\n\t\t  :class=\"bemClass\"\r\n\t\t>\r\n\t\t<!-- #endif -->\r\n    <!-- #ifndef MP -->\r\n    <button\r\n      :hover-start-time=\"Number(hoverStartTime)\"\r\n      :hover-stay-time=\"Number(hoverStayTime)\"\r\n      :form-type=\"formType\"\r\n      :open-type=\"openType\"\r\n      :app-parameter=\"appParameter\"\r\n      :hover-stop-propagation=\"hoverStopPropagation\"\r\n      :send-message-title=\"sendMessageTitle\"\r\n      :send-message-path=\"sendMessagePath\"\r\n      :lang=\"lang\"\r\n      :data-name=\"dataName\"\r\n      :session-from=\"sessionFrom\"\r\n      :send-message-img=\"sendMessageImg\"\r\n      :show-message-card=\"showMessageCard\"\r\n      :hover-class=\"!disabled && !loading ? 'uv-button--active' : ''\"\r\n      class=\"uv-button uv-reset-button\"\r\n      :style=\"[baseColor, $uv.addStyle(customStyle)]\"\r\n      @tap=\"clickHandler\"\r\n      :class=\"bemClass\"\r\n    >\r\n    <!-- #endif -->\r\n      <template v-if=\"loading\">\r\n        <uv-loading-icon\r\n          :mode=\"loadingMode\"\r\n          :size=\"loadingSize * 1.15\"\r\n          :color=\"loadingColor\"\r\n        ></uv-loading-icon>\r\n          <text\r\n            class=\"uv-button__loading-text\"\r\n            :style=\"[\r\n\t\t\t\t\t\t\t{ fontSize: textSize + 'px' },\r\n\t\t\t\t\t\t\t$uv.addStyle(customTextStyle)\r\n\t\t\t\t\t\t]\"\r\n          >{{ loadingText || text }}</text>\r\n      </template>\r\n      <template v-else>\r\n        <uv-icon\r\n          v-if=\"icon\"\r\n          :name=\"icon\"\r\n          :color=\"iconColorCom\"\r\n          :size=\"getIconSize\"\r\n          :customStyle=\"{ marginRight: '2px' }\"\r\n        ></uv-icon>\r\n        <slot>\r\n          <text\r\n            class=\"uv-button__text\"\r\n            :style=\"[\r\n\t\t\t\t\t\t\t{ fontSize: textSize + 'px' },\r\n\t\t\t\t\t\t\t$uv.addStyle(customTextStyle)\r\n\t\t\t\t\t\t]\"\r\n            >{{ text }}</text>\r\n        </slot>\r\n\t\t\t\t<slot name=\"suffix\"></slot>\r\n      </template>\r\n    </button>\r\n    <!-- #endif -->\r\n    <!-- #ifdef APP-NVUE -->\r\n    <view\r\n      :hover-start-time=\"Number(hoverStartTime)\"\r\n      :hover-stay-time=\"Number(hoverStayTime)\"\r\n      class=\"uv-button\"\r\n      :hover-class=\"\r\n        !disabled && !loading && !color && (plain || type === 'info')\r\n          ? 'uv-button--active--plain'\r\n          : !disabled && !loading && !plain\r\n          ? 'uv-button--active'\r\n          : ''\r\n      \"\r\n      @tap=\"clickHandler\"\r\n      :class=\"bemClass\"\r\n      :style=\"[baseColor, $uv.addStyle(customStyle)]\"\r\n    >\r\n      <template v-if=\"loading\">\r\n        <uv-loading-icon\r\n          :mode=\"loadingMode\"\r\n          :size=\"loadingSize * 1.15\"\r\n          :color=\"loadingColor\"\r\n        ></uv-loading-icon>\r\n        <text\r\n          class=\"uv-button__loading-text\"\r\n          :style=\"[nvueTextStyle,$uv.addStyle(customTextStyle)]\"\r\n          :class=\"[plain && `uv-button__text--plain--${type}`]\"\r\n          >{{ loadingText || text }}</text>\r\n      </template>\r\n      <template v-else>\r\n        <uv-icon\r\n          v-if=\"icon\"\r\n          :name=\"icon\"\r\n          :color=\"iconColorCom\"\r\n          :size=\"getIconSize\"\r\n        ></uv-icon>\r\n        <text\r\n          class=\"uv-button__text\"\r\n          :style=\"[\r\n            {\r\n              marginLeft: icon ? '2px' : 0,\r\n            },\r\n            nvueTextStyle,\r\n\t\t\t\t\t\t$uv.addStyle(customTextStyle)\r\n          ]\"\r\n          :class=\"[plain && `uv-button__text--plain--${type}`]\"\r\n          >{{ text }}</text>\r\n\t\t\t\t<slot name=\"suffix\"></slot>\r\n      </template>\r\n    </view>\r\n    <!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport throttle from '@/uni_modules/uv-ui-tools/libs/function/throttle.js';\r\nimport mpMixin from '@/uni_modules/uv-ui-tools/libs/mixin/mpMixin.js'\r\nimport mixin from '@/uni_modules/uv-ui-tools/libs/mixin/mixin.js'\r\nimport button from '@/uni_modules/uv-ui-tools/libs/mixin/button.js'\r\nimport openType from '@/uni_modules/uv-ui-tools/libs/mixin/openType.js'\r\nimport props from \"./props.js\";\r\n/**\r\n * button 按钮\r\n * @description Button 按钮\r\n * @tutorial https://www.uvui.cn/components/button.html\r\n * @property {Boolean}\t\t\thairline\t\t\t\t是否显示按钮的细边框 (默认 true )\r\n * @property {String}\t\t\ttype\t\t\t\t\t按钮的预置样式，info，primary，error，warning，success (默认 'info' )\r\n * @property {String}\t\t\tsize\t\t\t\t\t按钮尺寸，large，normal，mini （默认 normal）\r\n * @property {String}\t\t\tshape\t\t\t\t\t按钮形状，circle（两边为半圆），square（带圆角） （默认 'square' ）\r\n * @property {Boolean}\t\t\tplain\t\t\t\t\t按钮是否镂空，背景色透明 （默认 false）\r\n * @property {Boolean}\t\t\tdisabled\t\t\t\t是否禁用 （默认 false）\r\n * @property {Boolean}\t\t\tloading\t\t\t\t\t按钮名称前是否带 loading 图标(App-nvue 平台，在 ios 上为雪花，Android上为圆圈) （默认 false）\r\n * @property {String | Number}\tloadingText\t\t\t\t加载中提示文字\r\n * @property {String}\t\t\tloadingMode\t\t\t\t加载状态图标类型 （默认 'spinner' ）\r\n * @property {String | Number}\tloadingSize\t\t\t\t加载图标大小 （默认 15 ）\r\n * @property {String}\t\t\topenType\t\t\t\t开放能力，具体请看uniapp稳定关于button组件部分说明\r\n * @property {String}\t\t\tformType\t\t\t\t用于 <form> 组件，点击分别会触发 <form> 组件的 submit/reset 事件\r\n * @property {String}\t\t\tappParameter\t\t\t打开 APP 时，向 APP 传递的参数，open-type=launchApp时有效 （注：只微信小程序、QQ小程序有效）\r\n * @property {Boolean}\t\t\thoverStopPropagation\t指定是否阻止本节点的祖先节点出现点击态，微信小程序有效（默认 true ）\r\n * @property {String}\t\t\tlang\t\t\t\t\t指定返回用户信息的语言，zh_CN 简体中文，zh_TW 繁体中文，en 英文（默认 en ）\r\n * @property {String}\t\t\tsessionFrom\t\t\t\t会话来源，openType=\"contact\"时有效\r\n * @property {String}\t\t\tsendMessageTitle\t\t会话内消息卡片标题，openType=\"contact\"时有效\r\n * @property {String}\t\t\tsendMessagePath\t\t\t会话内消息卡片点击跳转小程序路径，openType=\"contact\"时有效\r\n * @property {String}\t\t\tsendMessageImg\t\t\t会话内消息卡片图片，openType=\"contact\"时有效\r\n * @property {Boolean}\t\t\tshowMessageCard\t\t\t是否显示会话内消息卡片，设置此参数为 true，用户进入客服会话会在右下角显示\"可能要发送的小程序\"提示，用户点击后可以快速发送小程序消息，openType=\"contact\"时有效（默认false）\r\n * @property {String}\t\t\tdataName\t\t\t\t额外传参参数，用于小程序的data-xxx属性，通过target.dataset.name获取\r\n * @property {String | Number}\tthrottleTime\t\t\t节流，一定时间内只能触发一次 （默认 0 )\r\n * @property {String | Number}\thoverStartTime\t\t\t按住后多久出现点击态，单位毫秒 （默认 0 )\r\n * @property {String | Number}\thoverStayTime\t\t\t手指松开后点击态保留时间，单位毫秒 （默认 200 )\r\n * @property {String | Number}\ttext\t\t\t\t\t按钮文字，之所以通过props传入，是因为slot传入的话（注：nvue中无法控制文字的样式）\r\n * @property {String}\t\t\ticon\t\t\t\t\t按钮图标\r\n * @property {String}\t\t\ticonColor\t\t\t\t按钮图标颜色\r\n * @property {String}\t\t\tcolor\t\t\t\t\t按钮颜色，支持传入linear-gradient渐变色\r\n * @property {Object}\t\t\tcustomStyle\t\t\t\t定义需要用到的外部样式\r\n * @event {Function}\tclick\t\t\t非禁止并且非加载中，才能点击\r\n * @event {Function}\tgetphonenumber\topen-type=\"getPhoneNumber\"时有效\r\n * @event {Function}\tgetuserinfo\t\t用户点击该按钮时，会返回获取到的用户信息，从返回参数的detail中获取到的值同uni.getUserInfo\r\n * @event {Function}\terror\t\t\t当使用开放能力时，发生错误的回调\r\n * @event {Function}\topensetting\t\t在打开授权设置页并关闭后回调\r\n * @event {Function}\tlaunchapp\t\t打开 APP 成功的回调\r\n * @example <uv-button>月落</uv-button>\r\n */\r\nexport default {\r\n\t\tname: \"uv-button\",\r\n\t\t// #ifdef MP\r\n\t\tmixins: [mpMixin, mixin, button, openType, props],\r\n\t\t// #endif\r\n\t\t// #ifndef MP\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\t// #endif\r\n\t\temits: ['click'],\r\n\t\tdata() {\r\n\t\t\treturn {};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 生成bem风格的类名\r\n\t\t\tbemClass() {\r\n\t\t\t\t// this.bem为一个computed变量，在mixin中\r\n\t\t\t\tif (!this.color) {\r\n\t\t\t\t\treturn this.bem(\"button\",\r\n\t\t\t\t\t\t[\"type\", \"shape\", \"size\"],\r\n\t\t\t\t\t\t[\"disabled\", \"plain\", \"hairline\"]);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 由于nvue的原因，在有color参数时，不需要传入type，否则会生成type相关的类型，影响最终的样式\r\n\t\t\t\t\treturn this.bem(\"button\",\r\n\t\t\t\t\t\t[\"shape\", \"size\"],\r\n\t\t\t\t\t\t[\"disabled\", \"plain\", \"hairline\"]);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tloadingColor() {\r\n\t\t\t\tif (this.plain) {\r\n\t\t\t\t\t// 如果有设置color值，则用color值，否则使用type主题颜色\r\n\t\t\t\t\treturn this.color ? this.color : '#3c9cff';\r\n\t\t\t\t}\r\n\t\t\t\tif (this.type === \"info\") {\r\n\t\t\t\t\treturn \"#c9c9c9\";\r\n\t\t\t\t}\r\n\t\t\t\treturn \"rgb(200, 200, 200)\";\r\n\t\t\t},\r\n\t\t\ticonColorCom() {\r\n\t\t\t\t// 如果是镂空状态，设置了color就用color值，否则使用主题颜色，\r\n\t\t\t\t// uv-icon的color能接受一个主题颜色的值\r\n\t\t\t\tif (this.iconColor) return this.iconColor;\r\n\t\t\t\tif (this.plain) {\r\n\t\t\t\t\treturn this.color ? this.color : this.type;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn this.type === \"info\" ? \"#000000\" : \"#ffffff\";\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tbaseColor() {\r\n\t\t\t\tlet style = {};\r\n\t\t\t\tif (this.color) {\r\n\t\t\t\t\t// 针对自定义了color颜色的情况，镂空状态下，就是用自定义的颜色\r\n\t\t\t\t\tstyle.color = this.plain ? this.color : \"white\";\r\n\t\t\t\t\tif (!this.plain) {\r\n\t\t\t\t\t\t// 非镂空，背景色使用自定义的颜色\r\n\t\t\t\t\t\tstyle[\"background-color\"] = this.color;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.color.indexOf(\"gradient\") !== -1) {\r\n\t\t\t\t\t\t// 如果自定义的颜色为渐变色，不显示边框，以及通过backgroundImage设置渐变色\r\n\t\t\t\t\t\t// weex文档说明可以写borderWidth的形式，为什么这里需要分开写？\r\n\t\t\t\t\t\t// 因为weex是阿里巴巴为了部门业绩考核而做的你懂的东西，所以需要这么写才有效\r\n\t\t\t\t\t\tstyle.borderTopWidth = 0;\r\n\t\t\t\t\t\tstyle.borderRightWidth = 0;\r\n\t\t\t\t\t\tstyle.borderBottomWidth = 0;\r\n\t\t\t\t\t\tstyle.borderLeftWidth = 0;\r\n\t\t\t\t\t\tif (!this.plain) {\r\n\t\t\t\t\t\t\tstyle.backgroundImage = this.color;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 非渐变色，则设置边框相关的属性\r\n\t\t\t\t\t\tstyle.borderColor = this.color;\r\n\t\t\t\t\t\tstyle.borderWidth = \"1px\";\r\n\t\t\t\t\t\tstyle.borderStyle = \"solid\";\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn style;\r\n\t\t\t},\r\n\t\t\t// nvue版本按钮的字体不会继承父组件的颜色，需要对每一个text组件进行单独的设置\r\n\t\t\tnvueTextStyle() {\r\n\t\t\t\tlet style = {};\r\n\t\t\t\t// 针对自定义了color颜色的情况，镂空状态下，就是用自定义的颜色\r\n\t\t\t\tif (this.type === \"info\") {\r\n\t\t\t\t\tstyle.color = \"#323233\";\r\n\t\t\t\t}\r\n\t\t\t\tif (this.color) {\r\n\t\t\t\t\tstyle.color = this.plain ? this.color : \"white\";\r\n\t\t\t\t}\r\n\t\t\t\tstyle.fontSize = this.textSize + \"px\";\r\n\t\t\t\treturn style;\r\n\t\t\t},\r\n\t\t\t// 字体大小\r\n\t\t\ttextSize() {\r\n\t\t\t\tlet fontSize = 14,\r\n\t\t\t\t\t{ size } = this;\r\n\t\t\t\tif (size === \"large\") fontSize = 16;\r\n\t\t\t\tif (size === \"normal\") fontSize = 14;\r\n\t\t\t\tif (size === \"small\") fontSize = 12;\r\n\t\t\t\tif (size === \"mini\") fontSize = 10;\r\n\t\t\t\treturn fontSize;\r\n\t\t\t},\r\n\t\t\t// 设置图标大小\r\n\t\t\tgetIconSize() {\r\n\t\t\t\tconst size = this.iconSize ? this.iconSize : this.textSize * 1.35;\r\n\t\t\t\treturn this.$uv.addUnit(size);\r\n\t\t\t},\r\n\t\t\t// 设置外层盒子的宽度，其他样式不需要\r\n\t\t\tbtnWrapperStyle() {\r\n\t\t\t\tconst style = {};\r\n\t\t\t\tconst customStyle = this.$uv.addStyle(this.customStyle);\r\n\t\t\t\tif(customStyle.width) style.width = customStyle.width;\r\n\t\t\t\treturn style;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tclickHandler() {\r\n\t\t\t\t// 非禁止并且非加载中，才能点击\r\n\t\t\t\tif (!this.disabled && !this.loading) {\r\n\t\t\t\t\t// 进行节流控制，每this.throttle毫秒内，只在开始处执行\r\n\t\t\t\t\tthrottle(() => {\r\n\t\t\t\t\t\tthis.$emit(\"click\");\r\n\t\t\t\t\t}, this.throttleTime);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n$show-reset-button: 1;\r\n@import '@/uni_modules/uv-ui-tools/libs/css/variable.scss';\r\n@import '@/uni_modules/uv-ui-tools/libs/css/components.scss';\r\n@import '@/uni_modules/uv-ui-tools/libs/css/color.scss';\r\n\r\n/* #ifndef APP-NVUE */\r\n@import \"./vue.scss\";\r\n/* #endif */\r\n\r\n/* #ifdef APP-NVUE */\r\n@import \"./nvue.scss\";\r\n/* #endif */\r\n\r\n$uv-button-uv-button-height: 40px !default;\r\n$uv-button-text-font-size: 15px !default;\r\n$uv-button-loading-text-font-size: 15px !default;\r\n$uv-button-loading-text-margin-left: 4px !default;\r\n$uv-button-large-width: 100% !default;\r\n$uv-button-large-height: 50px !default;\r\n$uv-button-normal-padding: 0 12px !default;\r\n$uv-button-large-padding: 0 15px !default;\r\n$uv-button-normal-font-size: 14px !default;\r\n$uv-button-small-min-width: 60px !default;\r\n$uv-button-small-height: 30px !default;\r\n$uv-button-small-padding: 0px 8px !default;\r\n$uv-button-mini-padding: 0px 8px !default;\r\n$uv-button-small-font-size: 12px !default;\r\n$uv-button-mini-height: 22px !default;\r\n$uv-button-mini-font-size: 10px !default;\r\n$uv-button-mini-min-width: 50px !default;\r\n$uv-button-disabled-opacity: 0.5 !default;\r\n$uv-button-info-color: #323233 !default;\r\n$uv-button-info-background-color: #fff !default;\r\n$uv-button-info-border-color: #ebedf0 !default;\r\n$uv-button-info-border-width: 1px !default;\r\n$uv-button-info-border-style: solid !default;\r\n$uv-button-success-color: #fff !default;\r\n$uv-button-success-background-color: $uv-success !default;\r\n$uv-button-success-border-color: $uv-button-success-background-color !default;\r\n$uv-button-success-border-width: 1px !default;\r\n$uv-button-success-border-style: solid !default;\r\n$uv-button-primary-color: #fff !default;\r\n$uv-button-primary-background-color: $uv-primary !default;\r\n$uv-button-primary-border-color: $uv-button-primary-background-color !default;\r\n$uv-button-primary-border-width: 1px !default;\r\n$uv-button-primary-border-style: solid !default;\r\n$uv-button-error-color: #fff !default;\r\n$uv-button-error-background-color: $uv-error !default;\r\n$uv-button-error-border-color: $uv-button-error-background-color !default;\r\n$uv-button-error-border-width: 1px !default;\r\n$uv-button-error-border-style: solid !default;\r\n$uv-button-warning-color: #fff !default;\r\n$uv-button-warning-background-color: $uv-warning !default;\r\n$uv-button-warning-border-color: $uv-button-warning-background-color !default;\r\n$uv-button-warning-border-width: 1px !default;\r\n$uv-button-warning-border-style: solid !default;\r\n$uv-button-block-width: 100% !default;\r\n$uv-button-circle-border-top-right-radius: 100px !default;\r\n$uv-button-circle-border-top-left-radius: 100px !default;\r\n$uv-button-circle-border-bottom-left-radius: 100px !default;\r\n$uv-button-circle-border-bottom-right-radius: 100px !default;\r\n$uv-button-square-border-top-right-radius: 3px !default;\r\n$uv-button-square-border-top-left-radius: 3px !default;\r\n$uv-button-square-border-bottom-left-radius: 3px !default;\r\n$uv-button-square-border-bottom-right-radius: 3px !default;\r\n$uv-button-icon-min-width: 1em !default;\r\n$uv-button-plain-background-color: #fff !default;\r\n$uv-button-hairline-border-width: 0.5px !default;\r\n\r\n.uv-button {\r\n    height: $uv-button-uv-button-height;\r\n    position: relative;\r\n    align-items: center;\r\n    justify-content: center;\r\n    @include flex;\r\n    /* #ifndef APP-NVUE */\r\n    box-sizing: border-box;\r\n    /* #endif */\r\n    flex-direction: row;\r\n\r\n    &__text {\r\n        font-size: $uv-button-text-font-size;\r\n    }\r\n\r\n    &__loading-text {\r\n        font-size: $uv-button-loading-text-font-size;\r\n        margin-left: $uv-button-loading-text-margin-left;\r\n    }\r\n\r\n    &--large {\r\n        /* #ifndef APP-NVUE */\r\n        width: $uv-button-large-width;\r\n        /* #endif */\r\n        height: $uv-button-large-height;\r\n        padding: $uv-button-large-padding;\r\n    }\r\n\r\n    &--normal {\r\n        padding: $uv-button-normal-padding;\r\n        font-size: $uv-button-normal-font-size;\r\n    }\r\n\r\n    &--small {\r\n        /* #ifndef APP-NVUE */\r\n        min-width: $uv-button-small-min-width;\r\n        /* #endif */\r\n        height: $uv-button-small-height;\r\n        padding: $uv-button-small-padding;\r\n        font-size: $uv-button-small-font-size;\r\n    }\r\n\r\n    &--mini {\r\n        height: $uv-button-mini-height;\r\n        font-size: $uv-button-mini-font-size;\r\n        /* #ifndef APP-NVUE */\r\n        min-width: $uv-button-mini-min-width;\r\n        /* #endif */\r\n        padding: $uv-button-mini-padding;\r\n    }\r\n\r\n    &--disabled {\r\n        opacity: $uv-button-disabled-opacity;\r\n    }\r\n\r\n    &--info {\r\n        color: $uv-button-info-color;\r\n        background-color: $uv-button-info-background-color;\r\n        border-color: $uv-button-info-border-color;\r\n        border-width: $uv-button-info-border-width;\r\n        border-style: $uv-button-info-border-style;\r\n    }\r\n\r\n    &--success {\r\n        color: $uv-button-success-color;\r\n        background-color: $uv-button-success-background-color;\r\n        border-color: $uv-button-success-border-color;\r\n        border-width: $uv-button-success-border-width;\r\n        border-style: $uv-button-success-border-style;\r\n    }\r\n\r\n    &--primary {\r\n        color: $uv-button-primary-color;\r\n        background-color: $uv-button-primary-background-color;\r\n        border-color: $uv-button-primary-border-color;\r\n        border-width: $uv-button-primary-border-width;\r\n        border-style: $uv-button-primary-border-style;\r\n    }\r\n\r\n    &--error {\r\n        color: $uv-button-error-color;\r\n        background-color: $uv-button-error-background-color;\r\n        border-color: $uv-button-error-border-color;\r\n        border-width: $uv-button-error-border-width;\r\n        border-style: $uv-button-error-border-style;\r\n    }\r\n\r\n    &--warning {\r\n        color: $uv-button-warning-color;\r\n        background-color: $uv-button-warning-background-color;\r\n        border-color: $uv-button-warning-border-color;\r\n        border-width: $uv-button-warning-border-width;\r\n        border-style: $uv-button-warning-border-style;\r\n    }\r\n\r\n    &--block {\r\n        @include flex;\r\n        width: $uv-button-block-width;\r\n    }\r\n\r\n    &--circle {\r\n        border-top-right-radius: $uv-button-circle-border-top-right-radius;\r\n        border-top-left-radius: $uv-button-circle-border-top-left-radius;\r\n        border-bottom-left-radius: $uv-button-circle-border-bottom-left-radius;\r\n        border-bottom-right-radius: $uv-button-circle-border-bottom-right-radius;\r\n    }\r\n\r\n    &--square {\r\n        border-bottom-left-radius: $uv-button-square-border-top-right-radius;\r\n        border-bottom-right-radius: $uv-button-square-border-top-left-radius;\r\n        border-top-left-radius: $uv-button-square-border-bottom-left-radius;\r\n        border-top-right-radius: $uv-button-square-border-bottom-right-radius;\r\n    }\r\n\r\n    &__icon {\r\n        /* #ifndef APP-NVUE */\r\n        min-width: $uv-button-icon-min-width;\r\n        line-height: inherit !important;\r\n        vertical-align: top;\r\n        /* #endif */\r\n    }\r\n\r\n    &--plain {\r\n        background-color: $uv-button-plain-background-color;\r\n    }\r\n\r\n    &--hairline {\r\n        border-width: $uv-button-hairline-border-width !important;\r\n    }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-button.vue?vue&type=style&index=0&id=7439ab6c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-button.vue?vue&type=style&index=0&id=7439ab6c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758881037414\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}