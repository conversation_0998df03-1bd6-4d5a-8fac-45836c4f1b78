<template>
  <view class="wrap">
    <u-popup
      :show="showFinishPopup"
      mode="bottom"
      @close="showFinishPopup = false"
      :closeable="true"
    >
      <view class="finish-popup">
        <view class="popup-title">完成工单</view>
        <view class="popup-content">
          <u-form
            labelPosition="top"
            labelWidth="auto"
            :model="finishForm"
            ref="finishForm"
          >
            <u-form-item
              borderBottom
              labelPosition="left"
              label="完成时间"
              required
            >
              <view
                style="
                  display: flex;
                  justify-content: flex-end;
                  align-items: center;
                "
              >
                <text @click="showFinishTimePicker = true"
                  >{{
                    dateFormat(
                      new Date(Number(finishForm.finishTime)),
                      "yyyy-MM-dd hh:mm"
                    ) || "青选择完成时间"
                  }}
                  <u-icon label="uView" size="40" name="arrow-right"></u-icon
                ></text>
              </view>
              <u-datetime-picker
                v-model="finishForm.finishTime"
                :show="showFinishTimePicker"
                @cancel="showFinishTimePicker = false"
                @confirm="showFinishTimePicker = false"
                mode="datetime"
                :visibleItemCount="5"
              ></u-datetime-picker>
            </u-form-item>
            <u-form-item borderBottom label="备注">
              <u-textarea
                v-model="finishForm.remark"
                border="none"
                placeholder="请输入备注信息"
              ></u-textarea>
            </u-form-item>
            <u-form-item borderBottom label="上传图片/视频">
              <uv-upload
                accept="media"
                @clickPreview="handleClickPreview"
                :fileList="finishForm.fileList"
                @afterRead="afterRead"
                @delete="handleDelete"
                multiple
                :maxCount="9"
              >
              </uv-upload>
            </u-form-item>
          </u-form>
        </view>
        <view class="popup-footer">
          <u-button type="primary" @click="submitFinish">提交</u-button>
        </view>
      </view>
    </u-popup>
    <u-modal
      :show="modalShow"
      @confirm="confirm"
      ref="uModal"
      title="确认接单"
      content="确认接单吗？"
      showCancelButton
      @cancel="modalShow = false"
      :asyncClose="true"
    ></u-modal>
    <view class="search-box">
      <u-search
        shape="square"
        v-model="keyWords"
        placeholder="工单名称"
        :showAction="false"
        @search="handleSearch"
      ></u-search>
    </view>
    <view class="" style="background-color: #fff; margin-top: 10rpx">
      <u-subsection
        :list="subList"
        keyName="label"
        @change="sectionChange"
        :current="currentStatus"
      ></u-subsection>
    </view>
    <view class="content_box">
      <u-list height="100%" @scrolltolower="scrolltolower">
        <u-list-item v-for="(item, index) in list" :key="item.id">
          <uni-card
            @click.stop="toDetail(item)"
            :border="false"
            margin="0 0 20rpx 0"
            :is-shadow="false"
          >
            <template #title>
              <view
                style="
                  padding: 10rpx 0 10rpx 0;
                  border-bottom: 2rpx solid #eee;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                "
              >
                <view class="">
                  <text style="font-weight: bolder">{{
                    item.objectName || "--"
                  }}</text>
                </view>
                <view class="" style="min-width: 120rpx;display: flex; align-items: center;gap: 10rpx">
                  <uni-tag
                    type="primary"
                    inverted
                    :text="
                      subList.find((i) => i.value == item.objectStatus).label
                    "
                  ></uni-tag>
                  <uni-tag
                    type="primary"
                   
                    :text="
                      payStatus.find((i) => i.value == item.payStatus).label
                    "
                  ></uni-tag>
                </view>
              </view>
            </template>
            <view>
              <view class="item">
                <text style="font-size: 28rpx; color: #555">服务客户：</text>
                <text style="color: #000">{{ item.finalCustomer }}</text>
              </view>
              <view
                style="
                  display: flex;
                  justify-content: space-between;
                  width: 100%;
                "
              >
                <view class="item" style="width: 50%">
                  <text style="font-size: 28rpx; color: #555">联系人：</text>
                  <text style="color: #000">{{ item.contact }}</text>
                </view>
                <view class="item" style="width: 50%">
                  <text style="font-size: 28rpx; color: #555">联系电话：</text>
                  <text style="color: #000">{{ item.contactPhone || "" }}</text>
                </view>
              </view>
              <view class="item">
                <view style="font-size: 28rpx; color: #555">服务地址：</view>
                <text style="color: #000">{{ item.distributionAddress }}</text>
              </view>
              <view class="item">
                <text style="font-size: 28rpx; color: #555">服务时间：</text>
                <text style="color: #000">{{ item.planTime }}</text>
              </view>
              <view class="item">
                <view style="font-size: 28rpx; color: #555">工单内容：</view>
                <text style="color: #000">{{ item.remark }}</text>
              </view>
            </view>
            <template #actions>
              <view slot="actions" class="card-actions">
                <view
                  style="
                    display: flex;
                    justify-content: space-around;
                    width: 100%;
                    align-items: center;
                  "
                >
                  <u--text
                    size="12"
                    :text="
                      item.objectStatus == 3
                        ? '工单待开始'
                        : item.objectStatus == 1
                        ? '工单进行中'
                        : '工单已完成'
                    "
                    type="primary"
                  ></u--text>
                  <view>
                    <u-button
                      text="开始"
                      v-if="item.objectStatus == 3"
                      size="mini"
                      @tap.stop="handleStart(item)"
                      type="primary"
                    ></u-button>

                    <u-button
                      text="完成"
                      v-if="item.objectStatus == 1"
                      @tap.stop="handleFinish(item)"
                      size="mini"
                      type="primary"
                    ></u-button>
                    <u-button
                      text="申请结算"
                      v-if="item.objectStatus == 2 && item.payStatus == null"
                      @tap.stop="handleSettled(item)"
                      size="mini"
                      type="primary"
                    ></u-button>
                  </view>
                </view>
              </view>
            </template>
          </uni-card>
        </u-list-item>
        <uni-load-more
          :status="list.length == page.total ? 'noMore' : 'loading'"
        ></uni-load-more>
      </u-list>
    </view>
    <u-modal
      :show="show"
      content="你还未完善手机号,姓名等信息,请点击确认去完善"
      @confirm="toUserInfo"
    ></u-modal>
    <u-popup
      :show="showSettlePopup"
      mode="bottom"
      @close="showSettlePopup = false"
      :closeable="true"
    >
      <view class="finish-popup">
        <view class="popup-title">申请结算</view>
        <view class="popup-content">
          <u-form
            labelPosition="top"
            labelWidth="auto"
            :model="settleForm"
            ref="settleForm"
          >
            <!-- 结算金额 -->
            <u-form-item borderBottom label="结算金额">
              <u--input
                v-model="settleForm.totalPrice"
                placeholder="请输入结算金额"
                border="none"
                type="number"
              />
            </u-form-item>
            <u-form-item borderBottom label="备注">
              <u-textarea
                v-model="settleForm.applyContent"
                border="none"
                placeholder="请输入备注信息"
              ></u-textarea>
            </u-form-item>
          </u-form>
        </view>
        <view class="popup-footer">
          <u-button type="primary" @click="submitSettle">提交</u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>
<script>
import { dateFormat } from "../../utils/date";
import http from "../../http/api.js";
;
export default {
  name: "workerOrder",
  data() {
    return {
      keyWords: "",
      currentStatus: 0,
      dateFormat,
      list: [],
      showFinishPopup: false,
      showFinishTimePicker: false,
      modalShow: false,
      finishForm: {
        finishTime: Number(new Date()),
        remark: "",
        fileList: [],
      },
      page: {
        size: 10,
        current: 1,
        total: 0,
      },
      subList: [
        {
          label: "待接单",
          value: 3,
        },
        {
          label: "进行中",
          value: 1,
        },
        {
          label: "已完成",
          value: 2,
        },
      ],
      payStatus: [
        {
          value: 0,
          label: "待审核",
        },
        {
          value: 1,
          label: "待付款",
        },
        {
          value: 2,
          label: "已付款",
        },
        {
          value: 3,
          label: "审核失败",
        },
      ],
      show: false,
      showSettlePopup: false,
      settleForm: {},
    };
  },
  onReady() {
    this.getList();
  },
  onShow() {
    this.validatePhone();
  },
  methods: {
    getList() {
      this.$u.api
        .getWorkerOrder({
          size: this.page.size,
          current: this.page.current,
          objectStatus: this.subList[this.currentStatus].value,
          objectName: this.keyWords,
        })
        .then((res) => {
          this.list = [...this.list, ...res.data.records];
          this.page.total = res.data.total;
        });
    },
    scrolltolower() {
      console.log(1111);

      if (this.list.length == this.page.total) return;
      this.page.current++;
      this.getList();
    },
    sectionChange(index) {
      this.currentStatus = index;
      this.page.current = 1;
      this.list = [];
      this.getList();
    },
    handleSearch() {
      this.list = [];
      this.page.current = 1;
      this.getList();
    },
    handleStart(item) {
      this.currentItem = item;
      this.modalShow = true;
    },
    confirm() {
      this.$u.api.startWorkerOrder(this.currentItem.id).then((res) => {
        this.modalShow = false;
        this.page.current = 1;
        this.list = [];
        this.getList();
      });
    },
    handleFinish(item) {
      this.showFinishPopup = true;
      this.currentItem = item;
    },
    afterRead(event) {
      console.log(event);
      const { file } = event;
      const indexAll = this.finishForm.fileList.length;
      file.forEach((item, index) => {
        this.finishForm.fileList.push({
          ...item,
          status: "uploading",
          message: "上传中",
          // url: item.thumb,
          index: indexAll + index,
        });
      });
      file.forEach((item, index) => {
        this.uploadFile(item.url, indexAll + index);
      });
    },
    uploadFile(url, index) {
      return new Promise((resolve) => {
        const params = {
          filePath: url,
          name: "file",
        };
        http.upload("/blade-resource/attach/upload", params).then((res) => {
          this.finishForm.fileList.find((item) => item.index == index).status =
            "success";
          this.finishForm.fileList.find((item) => item.index == index).message =
            "";
          this.finishForm.fileList.find((item) => item.index == index).url =
            res.data.link;
          this.finishForm.fileList.find((item) => item.index == index).id =
            res.data.id;
        });
      });
    },
    handleDelete({ file, index, name }) {
      console.log(file, index, name);
      this.finishForm.fileList.splice(index, 1);
    },
    handleClickPreview(url, lists, name) {
      console.log(url, lists, name);
    },

    submitFinish() {
      if (!this.finishForm.finishTime) {
        this.$u.toast("请选择完成时间");
        return;
      }
      const formData = {
        id: this.currentItem.id,
        finishTime: this.dateFormat(
          new Date(Number(this.finishForm.finishTime)),
          "yyyy-MM-dd hh:mm:ss"
        ),
        completeRemark: this.finishForm.remark,
        completeFiles:
          this.finishForm.fileList &&
          this.finishForm.fileList.map((item) => item.id).join(","),
      };
      this.$u.api
        .finishWorkerOrder(formData)
        .then((res) => {
          this.$u.toast("提交成功");
          this.showFinishPopup = false;
          this.finishForm = {
            finishTime: "",
            remark: "",
            fileList: [],
          };
          this.getList();
        })
        .catch((err) => {
          this.$u.toast(err.message || "提交失败");
        });
    },
    toDetail(item) {
      uni.navigateTo({
        url: "/pages/wokerOrder/wokerOrderDetail?id=" + item.id,
      });
    },
    validatePhone() {
      this.$u.api.userInfo().then((res) => {
        ;

        if (!res.data.phone) {
          this.show = true;
        } else {
          console.log(res);
          this.show = false;
        }
      });
    },
    toUserInfo() {
      uni.navigateTo({
        url: "/pages/person/userInfo",
      });
    },
    handleSettled(item) {
      this.showSettlePopup = true;
      this.settleForm = {
        objectId: item.id,
        totalPrice: item.orderPrice,
      };
    },
    submitSettle() {
      this.$u.api.applySettlement(this.settleForm).then((res) => {
        this.$u.toast("申请成功");
        this.showSettlePopup = false;
        this.settleForm = {
          objectId: "",
          totalPrice: "",
        };
        this.list = [];
        this.page.current = 1;
        this.getList();
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.wrap {
  padding: 10rpx;
  box-sizing: border-box;
  height: 100vh;

  .search-box {
    height: 68rpx;
  }

  .content_box {
    box-sizing: border-box;
    margin-top: 10rpx;
    height: calc(100% - 68rpx - 20rpx - 64rpx);
    color: #000;
  }

  .item {
    margin-bottom: 10rpx;
  }
}

.card-actions {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  height: 45px;
  border-top: 1px #eee solid;
}

.card-actions-item {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.card-actions-item-text {
  font-size: 12px;
  color: #666;
  margin-left: 5px;
}

.circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid var(--el-color-success);
  color: var(--el-color-success);
  line-height: 50px;
  text-align: center;
  margin-right: 10px;
}

.finish-popup {
  padding: 30rpx;

  .popup-title {
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30rpx;
  }

  .popup-content {
    margin-bottom: 30rpx;
  }

  .popup-footer {
    padding: 20rpx 0;
  }
}
</style>
