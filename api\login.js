import http from '@/http/api.js'

// 获取token
const token = (tenantId, username, password, type) => {
	return http.request({
		url: '/blade-auth/oauth/token',
		method: 'POST',
		header: {
			'Tenant-Id': tenantId
		},
		params: {
			tenantId,
			username,
			password,
			grant_type: "password",
			scope: "all",
			type
		}
	})
}
// 小程序登录获取token
const wxToken = ({
	code,
	phone,
}) => {
	return http.request({
		url: '/blade-auth/oauth/token',
		method: 'POST',
		
		params: {
			code,
			grant_type: "work_order_mini",
			scope: "all",
			phone,
		}
	})
}

const refreshToken = (refresh_token, tenantId = '806174') => {
	return http.request({
		url: '/blade-auth/oauth/token',
		method: 'post',
		headers: {
			'Tenant-Id': tenantId
		},
		params: {
			tenantId,
			refresh_token,
			grant_type: "refresh_token",
			scope: "all",
		}
	})
}




export default {
	token,
	wxToken,

	refreshToken
}