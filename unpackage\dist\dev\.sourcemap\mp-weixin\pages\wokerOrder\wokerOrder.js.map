{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrder.vue?43bd", "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrder.vue?f03d", "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrder.vue?49b7", "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrder.vue?f4d2", "uni-app:///pages/wokerOrder/wokerOrder.vue", "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrder.vue?3716", "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrder.vue?2295"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "key<PERSON>ords", "currentStatus", "dateFormat", "list", "showFinishPopup", "showFinishTimePicker", "modalShow", "finishForm", "finishTime", "remark", "fileList", "page", "size", "current", "total", "subList", "label", "value", "payStatus", "show", "showSettlePopup", "settleForm", "onReady", "onShow", "methods", "getList", "getWorkerOrder", "objectStatus", "objectName", "then", "scrolltolower", "console", "sectionChange", "handleSearch", "handleStart", "confirm", "handleFinish", "afterRead", "file", "item", "status", "message", "index", "uploadFile", "filePath", "http", "res", "handleDelete", "handleClickPreview", "submit<PERSON><PERSON>sh", "id", "completeRemark", "completeFiles", "finishWorkerOrder", "catch", "toDetail", "uni", "url", "validatePhone", "toUserInfo", "handleSettled", "objectId", "totalPrice", "submitSettle"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,qUAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxKA;AAAA;AAAA;AAAA;AAAumB,CAAgB,onBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACuR3nB;AACA;AAAA;AAAA;AACA;AAAA,eACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC,UACA;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC,YACA;QACAD;QACAD;MACA,GACA;QACAC;QACAD;MACA,GACA;QACAC;QACAD;MACA,GACA;QACAC;QACAD;MACA,EACA;MACAG;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA,YACAC;QACAd;QACAC;QACAc;QACAC;MACA,GACAC;QACA;QACA;MACA;IACA;IACAC;MACAC;MAEA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACAN;MACA;MACA;MACAO;QACA,gEACAC;UACAC;UACAC;UACA;UACAC;QAAA,GACA;MACA;MACAJ;QACA;MACA;IACA;IACAK;MAAA;MACA;QACA;UACAC;UACA9C;QACA;QACA+C;UACA;YAAA;UAAA,YACA;UACA;YAAA;UAAA,aACA;UACA;YAAA;UAAA,SACAC;UACA;YAAA;UAAA,QACAA;QACA;MACA;IACA;IACAC;MAAA;QAAAL;QAAA5C;MACAiC;MACA;IACA;IACAiB;MACAjB;IACA;IAEAkB;MAAA;MACA;QACA;QACA;MACA;MACA;QACAC;QACA1C,4BACA,8CACA,sBACA;QACA2C;QACAC,eACA,4BACA;UAAA;QAAA;MACA;MACA,YACAC,4BACAxB;QACA;QACA;QACA;UACArB;UACAC;UACAC;QACA;QACA;MACA,GACA4C;QACA;MACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;QAEA;UACA;QACA;UACA3B;UACA;QACA;MACA;IACA;IACA4B;MACAH;QACAC;MACA;IACA;IACAG;MACA;MACA;QACAC;QACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;UACAF;UACAC;QACA;QACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChgBA;AAAA;AAAA;AAAA;AAA0qC,CAAgB,2nCAAG,EAAC,C;;;;;;;;;;;ACA9rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/wokerOrder/wokerOrder.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/wokerOrder/wokerOrder.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./wokerOrder.vue?vue&type=template&id=0ec3f686&scoped=true&\"\nvar renderjs\nimport script from \"./wokerOrder.vue?vue&type=script&lang=js&\"\nexport * from \"./wokerOrder.vue?vue&type=script&lang=js&\"\nimport style0 from \"./wokerOrder.vue?vue&type=style&index=0&id=0ec3f686&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0ec3f686\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/wokerOrder/wokerOrder.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wokerOrder.vue?vue&type=template&id=0ec3f686&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form/u-form\" */ \"@/uni_modules/uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uDatetimePicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker\" */ \"@/uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker.vue\"\n      )\n    },\n    uTextarea: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-textarea/u-textarea\" */ \"@/uni_modules/uview-ui/components/u-textarea/u-textarea.vue\"\n      )\n    },\n    uvUpload: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-upload/components/uv-upload/uv-upload\" */ \"@/uni_modules/uv-upload/components/uv-upload/uv-upload.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-modal/u-modal\" */ \"@/uni_modules/uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-search/u-search\" */ \"@/uni_modules/uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    uSubsection: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-subsection/u-subsection\" */ \"@/uni_modules/uview-ui/components/u-subsection/u-subsection.vue\"\n      )\n    },\n    uList: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-list/u-list\" */ \"@/uni_modules/uview-ui/components/u-list/u-list.vue\"\n      )\n    },\n    uListItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-list-item/u-list-item\" */ \"@/uni_modules/uview-ui/components/u-list-item/u-list-item.vue\"\n      )\n    },\n    uniCard: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-card/components/uni-card/uni-card\" */ \"@/uni_modules/uni-card/components/uni-card/uni-card.vue\"\n      )\n    },\n    uniTag: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-tag/components/uni-tag/uni-tag\" */ \"@/uni_modules/uni-tag/components/uni-tag/uni-tag.vue\"\n      )\n    },\n    \"u-Text\": function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u--text/u--text\" */ \"@/uni_modules/uview-ui/components/u--text/u--text.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u--input/u--input\" */ \"@/uni_modules/uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.dateFormat(\n      new Date(Number(_vm.finishForm.finishTime)),\n      \"yyyy-MM-dd hh:mm\"\n    ) || \"青选择完成时间\"\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = _vm.subList.find(function (i) {\n      return i.value == item.objectStatus\n    })\n    var g1 = _vm.payStatus.find(function (i) {\n      return i.value == item.payStatus\n    })\n    return {\n      $orig: $orig,\n      g0: g0,\n      g1: g1,\n    }\n  })\n  var g2 = _vm.list.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showFinishPopup = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showFinishTimePicker = true\n    }\n    _vm.e2 = function ($event) {\n      _vm.showFinishTimePicker = false\n    }\n    _vm.e3 = function ($event) {\n      _vm.showFinishTimePicker = false\n    }\n    _vm.e4 = function ($event) {\n      _vm.modalShow = false\n    }\n    _vm.e5 = function ($event) {\n      _vm.showSettlePopup = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wokerOrder.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wokerOrder.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"wrap\">\r\n    <u-popup\r\n      :show=\"showFinishPopup\"\r\n      mode=\"bottom\"\r\n      @close=\"showFinishPopup = false\"\r\n      :closeable=\"true\"\r\n    >\r\n      <view class=\"finish-popup\">\r\n        <view class=\"popup-title\">完成工单</view>\r\n        <view class=\"popup-content\">\r\n          <u-form\r\n            labelPosition=\"top\"\r\n            labelWidth=\"auto\"\r\n            :model=\"finishForm\"\r\n            ref=\"finishForm\"\r\n          >\r\n            <u-form-item\r\n              borderBottom\r\n              labelPosition=\"left\"\r\n              label=\"完成时间\"\r\n              required\r\n            >\r\n              <view\r\n                style=\"\r\n                  display: flex;\r\n                  justify-content: flex-end;\r\n                  align-items: center;\r\n                \"\r\n              >\r\n                <text @click=\"showFinishTimePicker = true\"\r\n                  >{{\r\n                    dateFormat(\r\n                      new Date(Number(finishForm.finishTime)),\r\n                      \"yyyy-MM-dd hh:mm\"\r\n                    ) || \"青选择完成时间\"\r\n                  }}\r\n                  <u-icon label=\"uView\" size=\"40\" name=\"arrow-right\"></u-icon\r\n                ></text>\r\n              </view>\r\n              <u-datetime-picker\r\n                v-model=\"finishForm.finishTime\"\r\n                :show=\"showFinishTimePicker\"\r\n                @cancel=\"showFinishTimePicker = false\"\r\n                @confirm=\"showFinishTimePicker = false\"\r\n                mode=\"datetime\"\r\n                :visibleItemCount=\"5\"\r\n              ></u-datetime-picker>\r\n            </u-form-item>\r\n            <u-form-item borderBottom label=\"备注\">\r\n              <u-textarea\r\n                v-model=\"finishForm.remark\"\r\n                border=\"none\"\r\n                placeholder=\"请输入备注信息\"\r\n              ></u-textarea>\r\n            </u-form-item>\r\n            <u-form-item borderBottom label=\"上传图片/视频\">\r\n              <uv-upload\r\n                accept=\"media\"\r\n                @clickPreview=\"handleClickPreview\"\r\n                :fileList=\"finishForm.fileList\"\r\n                @afterRead=\"afterRead\"\r\n                @delete=\"handleDelete\"\r\n                multiple\r\n                :maxCount=\"9\"\r\n              >\r\n              </uv-upload>\r\n            </u-form-item>\r\n          </u-form>\r\n        </view>\r\n        <view class=\"popup-footer\">\r\n          <u-button type=\"primary\" @click=\"submitFinish\">提交</u-button>\r\n        </view>\r\n      </view>\r\n    </u-popup>\r\n    <u-modal\r\n      :show=\"modalShow\"\r\n      @confirm=\"confirm\"\r\n      ref=\"uModal\"\r\n      title=\"确认接单\"\r\n      content=\"确认接单吗？\"\r\n      showCancelButton\r\n      @cancel=\"modalShow = false\"\r\n      :asyncClose=\"true\"\r\n    ></u-modal>\r\n    <view class=\"search-box\">\r\n      <u-search\r\n        shape=\"square\"\r\n        v-model=\"keyWords\"\r\n        placeholder=\"工单名称\"\r\n        :showAction=\"false\"\r\n        @search=\"handleSearch\"\r\n      ></u-search>\r\n    </view>\r\n    <view class=\"\" style=\"background-color: #fff; margin-top: 10rpx\">\r\n      <u-subsection\r\n        :list=\"subList\"\r\n        keyName=\"label\"\r\n        @change=\"sectionChange\"\r\n        :current=\"currentStatus\"\r\n      ></u-subsection>\r\n    </view>\r\n    <view class=\"content_box\">\r\n      <u-list height=\"100%\" @scrolltolower=\"scrolltolower\">\r\n        <u-list-item v-for=\"(item, index) in list\" :key=\"item.id\">\r\n          <uni-card\r\n            @click.stop=\"toDetail(item)\"\r\n            :border=\"false\"\r\n            margin=\"0 0 20rpx 0\"\r\n            :is-shadow=\"false\"\r\n          >\r\n            <template #title>\r\n              <view\r\n                style=\"\r\n                  padding: 10rpx 0 10rpx 0;\r\n                  border-bottom: 2rpx solid #eee;\r\n                  display: flex;\r\n                  justify-content: space-between;\r\n                  align-items: center;\r\n                \"\r\n              >\r\n                <view class=\"\">\r\n                  <text style=\"font-weight: bolder\">{{\r\n                    item.objectName || \"--\"\r\n                  }}</text>\r\n                </view>\r\n                <view class=\"\" style=\"min-width: 120rpx;display: flex; align-items: center;gap: 10rpx\">\r\n                  <uni-tag\r\n                    type=\"primary\"\r\n                    inverted\r\n                    :text=\"\r\n                      subList.find((i) => i.value == item.objectStatus).label\r\n                    \"\r\n                  ></uni-tag>\r\n                  <uni-tag\r\n                    type=\"primary\"\r\n                   \r\n                    :text=\"\r\n                      payStatus.find((i) => i.value == item.payStatus).label\r\n                    \"\r\n                  ></uni-tag>\r\n                </view>\r\n              </view>\r\n            </template>\r\n            <view>\r\n              <view class=\"item\">\r\n                <text style=\"font-size: 28rpx; color: #555\">服务客户：</text>\r\n                <text style=\"color: #000\">{{ item.finalCustomer }}</text>\r\n              </view>\r\n              <view\r\n                style=\"\r\n                  display: flex;\r\n                  justify-content: space-between;\r\n                  width: 100%;\r\n                \"\r\n              >\r\n                <view class=\"item\" style=\"width: 50%\">\r\n                  <text style=\"font-size: 28rpx; color: #555\">联系人：</text>\r\n                  <text style=\"color: #000\">{{ item.contact }}</text>\r\n                </view>\r\n                <view class=\"item\" style=\"width: 50%\">\r\n                  <text style=\"font-size: 28rpx; color: #555\">联系电话：</text>\r\n                  <text style=\"color: #000\">{{ item.contactPhone || \"\" }}</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"item\">\r\n                <view style=\"font-size: 28rpx; color: #555\">服务地址：</view>\r\n                <text style=\"color: #000\">{{ item.distributionAddress }}</text>\r\n              </view>\r\n              <view class=\"item\">\r\n                <text style=\"font-size: 28rpx; color: #555\">服务时间：</text>\r\n                <text style=\"color: #000\">{{ item.planTime }}</text>\r\n              </view>\r\n              <view class=\"item\">\r\n                <view style=\"font-size: 28rpx; color: #555\">工单内容：</view>\r\n                <text style=\"color: #000\">{{ item.remark }}</text>\r\n              </view>\r\n            </view>\r\n            <template #actions>\r\n              <view slot=\"actions\" class=\"card-actions\">\r\n                <view\r\n                  style=\"\r\n                    display: flex;\r\n                    justify-content: space-around;\r\n                    width: 100%;\r\n                    align-items: center;\r\n                  \"\r\n                >\r\n                  <u--text\r\n                    size=\"12\"\r\n                    :text=\"\r\n                      item.objectStatus == 3\r\n                        ? '工单待开始'\r\n                        : item.objectStatus == 1\r\n                        ? '工单进行中'\r\n                        : '工单已完成'\r\n                    \"\r\n                    type=\"primary\"\r\n                  ></u--text>\r\n                  <view>\r\n                    <u-button\r\n                      text=\"开始\"\r\n                      v-if=\"item.objectStatus == 3\"\r\n                      size=\"mini\"\r\n                      @tap.stop=\"handleStart(item)\"\r\n                      type=\"primary\"\r\n                    ></u-button>\r\n\r\n                    <u-button\r\n                      text=\"完成\"\r\n                      v-if=\"item.objectStatus == 1\"\r\n                      @tap.stop=\"handleFinish(item)\"\r\n                      size=\"mini\"\r\n                      type=\"primary\"\r\n                    ></u-button>\r\n                    <u-button\r\n                      text=\"申请结算\"\r\n                      v-if=\"item.objectStatus == 2 && item.payStatus == null\"\r\n                      @tap.stop=\"handleSettled(item)\"\r\n                      size=\"mini\"\r\n                      type=\"primary\"\r\n                    ></u-button>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </template>\r\n          </uni-card>\r\n        </u-list-item>\r\n        <uni-load-more\r\n          :status=\"list.length == page.total ? 'noMore' : 'loading'\"\r\n        ></uni-load-more>\r\n      </u-list>\r\n    </view>\r\n    <u-modal\r\n      :show=\"show\"\r\n      content=\"你还未完善手机号,姓名等信息,请点击确认去完善\"\r\n      @confirm=\"toUserInfo\"\r\n    ></u-modal>\r\n    <u-popup\r\n      :show=\"showSettlePopup\"\r\n      mode=\"bottom\"\r\n      @close=\"showSettlePopup = false\"\r\n      :closeable=\"true\"\r\n    >\r\n      <view class=\"finish-popup\">\r\n        <view class=\"popup-title\">申请结算</view>\r\n        <view class=\"popup-content\">\r\n          <u-form\r\n            labelPosition=\"top\"\r\n            labelWidth=\"auto\"\r\n            :model=\"settleForm\"\r\n            ref=\"settleForm\"\r\n          >\r\n            <!-- 结算金额 -->\r\n            <u-form-item borderBottom label=\"结算金额\">\r\n              <u--input\r\n                v-model=\"settleForm.totalPrice\"\r\n                placeholder=\"请输入结算金额\"\r\n                border=\"none\"\r\n                type=\"number\"\r\n              />\r\n            </u-form-item>\r\n            <u-form-item borderBottom label=\"备注\">\r\n              <u-textarea\r\n                v-model=\"settleForm.applyContent\"\r\n                border=\"none\"\r\n                placeholder=\"请输入备注信息\"\r\n              ></u-textarea>\r\n            </u-form-item>\r\n          </u-form>\r\n        </view>\r\n        <view class=\"popup-footer\">\r\n          <u-button type=\"primary\" @click=\"submitSettle\">提交</u-button>\r\n        </view>\r\n      </view>\r\n    </u-popup>\r\n  </view>\r\n</template>\r\n<script>\r\nimport { dateFormat } from \"../../utils/date\";\r\nimport http from \"../../http/api.js\";\r\n;\r\nexport default {\r\n  name: \"workerOrder\",\r\n  data() {\r\n    return {\r\n      keyWords: \"\",\r\n      currentStatus: 0,\r\n      dateFormat,\r\n      list: [],\r\n      showFinishPopup: false,\r\n      showFinishTimePicker: false,\r\n      modalShow: false,\r\n      finishForm: {\r\n        finishTime: Number(new Date()),\r\n        remark: \"\",\r\n        fileList: [],\r\n      },\r\n      page: {\r\n        size: 10,\r\n        current: 1,\r\n        total: 0,\r\n      },\r\n      subList: [\r\n        {\r\n          label: \"待接单\",\r\n          value: 3,\r\n        },\r\n        {\r\n          label: \"进行中\",\r\n          value: 1,\r\n        },\r\n        {\r\n          label: \"已完成\",\r\n          value: 2,\r\n        },\r\n      ],\r\n      payStatus: [\r\n        {\r\n          value: 0,\r\n          label: \"待审核\",\r\n        },\r\n        {\r\n          value: 1,\r\n          label: \"待付款\",\r\n        },\r\n        {\r\n          value: 2,\r\n          label: \"已付款\",\r\n        },\r\n        {\r\n          value: 3,\r\n          label: \"审核失败\",\r\n        },\r\n      ],\r\n      show: false,\r\n      showSettlePopup: false,\r\n      settleForm: {},\r\n    };\r\n  },\r\n  onReady() {\r\n    this.getList();\r\n  },\r\n  onShow() {\r\n    this.validatePhone();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.$u.api\r\n        .getWorkerOrder({\r\n          size: this.page.size,\r\n          current: this.page.current,\r\n          objectStatus: this.subList[this.currentStatus].value,\r\n          objectName: this.keyWords,\r\n        })\r\n        .then((res) => {\r\n          this.list = [...this.list, ...res.data.records];\r\n          this.page.total = res.data.total;\r\n        });\r\n    },\r\n    scrolltolower() {\r\n      console.log(1111);\r\n\r\n      if (this.list.length == this.page.total) return;\r\n      this.page.current++;\r\n      this.getList();\r\n    },\r\n    sectionChange(index) {\r\n      this.currentStatus = index;\r\n      this.page.current = 1;\r\n      this.list = [];\r\n      this.getList();\r\n    },\r\n    handleSearch() {\r\n      this.list = [];\r\n      this.page.current = 1;\r\n      this.getList();\r\n    },\r\n    handleStart(item) {\r\n      this.currentItem = item;\r\n      this.modalShow = true;\r\n    },\r\n    confirm() {\r\n      this.$u.api.startWorkerOrder(this.currentItem.id).then((res) => {\r\n        this.modalShow = false;\r\n        this.page.current = 1;\r\n        this.list = [];\r\n        this.getList();\r\n      });\r\n    },\r\n    handleFinish(item) {\r\n      this.showFinishPopup = true;\r\n      this.currentItem = item;\r\n    },\r\n    afterRead(event) {\r\n      console.log(event);\r\n      const { file } = event;\r\n      const indexAll = this.finishForm.fileList.length;\r\n      file.forEach((item, index) => {\r\n        this.finishForm.fileList.push({\r\n          ...item,\r\n          status: \"uploading\",\r\n          message: \"上传中\",\r\n          // url: item.thumb,\r\n          index: indexAll + index,\r\n        });\r\n      });\r\n      file.forEach((item, index) => {\r\n        this.uploadFile(item.url, indexAll + index);\r\n      });\r\n    },\r\n    uploadFile(url, index) {\r\n      return new Promise((resolve) => {\r\n        const params = {\r\n          filePath: url,\r\n          name: \"file\",\r\n        };\r\n        http.upload(\"/blade-resource/attach/upload\", params).then((res) => {\r\n          this.finishForm.fileList.find((item) => item.index == index).status =\r\n            \"success\";\r\n          this.finishForm.fileList.find((item) => item.index == index).message =\r\n            \"\";\r\n          this.finishForm.fileList.find((item) => item.index == index).url =\r\n            res.data.link;\r\n          this.finishForm.fileList.find((item) => item.index == index).id =\r\n            res.data.id;\r\n        });\r\n      });\r\n    },\r\n    handleDelete({ file, index, name }) {\r\n      console.log(file, index, name);\r\n      this.finishForm.fileList.splice(index, 1);\r\n    },\r\n    handleClickPreview(url, lists, name) {\r\n      console.log(url, lists, name);\r\n    },\r\n\r\n    submitFinish() {\r\n      if (!this.finishForm.finishTime) {\r\n        this.$u.toast(\"请选择完成时间\");\r\n        return;\r\n      }\r\n      const formData = {\r\n        id: this.currentItem.id,\r\n        finishTime: this.dateFormat(\r\n          new Date(Number(this.finishForm.finishTime)),\r\n          \"yyyy-MM-dd hh:mm:ss\"\r\n        ),\r\n        completeRemark: this.finishForm.remark,\r\n        completeFiles:\r\n          this.finishForm.fileList &&\r\n          this.finishForm.fileList.map((item) => item.id).join(\",\"),\r\n      };\r\n      this.$u.api\r\n        .finishWorkerOrder(formData)\r\n        .then((res) => {\r\n          this.$u.toast(\"提交成功\");\r\n          this.showFinishPopup = false;\r\n          this.finishForm = {\r\n            finishTime: \"\",\r\n            remark: \"\",\r\n            fileList: [],\r\n          };\r\n          this.getList();\r\n        })\r\n        .catch((err) => {\r\n          this.$u.toast(err.message || \"提交失败\");\r\n        });\r\n    },\r\n    toDetail(item) {\r\n      uni.navigateTo({\r\n        url: \"/pages/wokerOrder/wokerOrderDetail?id=\" + item.id,\r\n      });\r\n    },\r\n    validatePhone() {\r\n      this.$u.api.userInfo().then((res) => {\r\n        ;\r\n\r\n        if (!res.data.phone) {\r\n          this.show = true;\r\n        } else {\r\n          console.log(res);\r\n          this.show = false;\r\n        }\r\n      });\r\n    },\r\n    toUserInfo() {\r\n      uni.navigateTo({\r\n        url: \"/pages/person/userInfo\",\r\n      });\r\n    },\r\n    handleSettled(item) {\r\n      this.showSettlePopup = true;\r\n      this.settleForm = {\r\n        objectId: item.id,\r\n        totalPrice: item.orderPrice,\r\n      };\r\n    },\r\n    submitSettle() {\r\n      this.$u.api.applySettlement(this.settleForm).then((res) => {\r\n        this.$u.toast(\"申请成功\");\r\n        this.showSettlePopup = false;\r\n        this.settleForm = {\r\n          objectId: \"\",\r\n          totalPrice: \"\",\r\n        };\r\n        this.list = [];\r\n        this.page.current = 1;\r\n        this.getList();\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.wrap {\r\n  padding: 10rpx;\r\n  box-sizing: border-box;\r\n  height: 100vh;\r\n\r\n  .search-box {\r\n    height: 68rpx;\r\n  }\r\n\r\n  .content_box {\r\n    box-sizing: border-box;\r\n    margin-top: 10rpx;\r\n    height: calc(100% - 68rpx - 20rpx - 64rpx);\r\n    color: #000;\r\n  }\r\n\r\n  .item {\r\n    margin-bottom: 10rpx;\r\n  }\r\n}\r\n\r\n.card-actions {\r\n  display: flex;\r\n  flex-direction: row;\r\n  justify-content: space-around;\r\n  align-items: center;\r\n  height: 45px;\r\n  border-top: 1px #eee solid;\r\n}\r\n\r\n.card-actions-item {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n}\r\n\r\n.card-actions-item-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-left: 5px;\r\n}\r\n\r\n.circle {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  border: 1px solid var(--el-color-success);\r\n  color: var(--el-color-success);\r\n  line-height: 50px;\r\n  text-align: center;\r\n  margin-right: 10px;\r\n}\r\n\r\n.finish-popup {\r\n  padding: 30rpx;\r\n\r\n  .popup-title {\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    text-align: center;\r\n    margin-bottom: 30rpx;\r\n  }\r\n\r\n  .popup-content {\r\n    margin-bottom: 30rpx;\r\n  }\r\n\r\n  .popup-footer {\r\n    padding: 20rpx 0;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wokerOrder.vue?vue&type=style&index=0&id=0ec3f686&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wokerOrder.vue?vue&type=style&index=0&id=0ec3f686&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759027613613\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}