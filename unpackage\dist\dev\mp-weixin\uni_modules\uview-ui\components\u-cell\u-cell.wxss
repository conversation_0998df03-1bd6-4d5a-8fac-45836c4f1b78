@charset "UTF-8";
/* 水平间距 */
/* 水平间距 */
view.data-v-1c4434ae, scroll-view.data-v-1c4434ae, swiper-item.data-v-1c4434ae {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-cell__body.data-v-1c4434ae {
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  padding: 10px 15px;
  font-size: 15px;
  color: #303133;
  align-items: center;
}
.u-cell__body__content.data-v-1c4434ae {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}
.u-cell__body--large.data-v-1c4434ae {
  padding-top: 13px;
  padding-bottom: 13px;
}
.u-cell__left-icon-wrap.data-v-1c4434ae, .u-cell__right-icon-wrap.data-v-1c4434ae {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 16px;
}
.u-cell__left-icon-wrap.data-v-1c4434ae {
  margin-right: 4px;
}
.u-cell__right-icon-wrap.data-v-1c4434ae {
  margin-left: 4px;
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
}
.u-cell__right-icon-wrap--up.data-v-1c4434ae {
  -webkit-transform: rotate(-90deg);
          transform: rotate(-90deg);
}
.u-cell__right-icon-wrap--down.data-v-1c4434ae {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}
.u-cell__title.data-v-1c4434ae {
  flex: 1;
}
.u-cell__title-text.data-v-1c4434ae {
  font-size: 15px;
  line-height: 22px;
  color: #303133;
}
.u-cell__title-text--large.data-v-1c4434ae {
  font-size: 16px;
}
.u-cell__label.data-v-1c4434ae {
  margin-top: 5px;
  font-size: 12px;
  color: #909193;
  line-height: 18px;
}
.u-cell__label--large.data-v-1c4434ae {
  font-size: 14px;
}
.u-cell__value.data-v-1c4434ae {
  text-align: right;
  font-size: 14px;
  line-height: 24px;
  color: #606266;
}
.u-cell__value--large.data-v-1c4434ae {
  font-size: 15px;
}
.u-cell--clickable.data-v-1c4434ae {
  background-color: #f3f4f6;
}
.u-cell--disabled.data-v-1c4434ae {
  color: #c8c9cc;
  cursor: not-allowed;
}
.u-cell--center.data-v-1c4434ae {
  align-items: center;
}

