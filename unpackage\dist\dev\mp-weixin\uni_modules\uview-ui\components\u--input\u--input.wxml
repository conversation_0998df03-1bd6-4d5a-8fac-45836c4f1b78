<uv-input vue-id="27e4185d-1" value="{{value}}" type="{{type}}" fixed="{{fixed}}" disabled="{{disabled}}" disabledColor="{{disabledColor}}" clearable="{{clearable}}" password="{{password}}" maxlength="{{maxlength}}" placeholder="{{placeholder}}" placeholderClass="{{placeholderClass}}" placeholderStyle="{{placeholderStyle}}" showWordLimit="{{showWordLimit}}" confirmType="{{confirmType}}" confirmHold="{{confirmHold}}" holdKeyboard="{{holdKeyboard}}" focus="{{focus}}" autoBlur="{{autoBlur}}" disableDefaultPadding="{{disableDefaultPadding}}" cursor="{{cursor}}" cursorSpacing="{{cursorSpacing}}" selectionStart="{{selectionStart}}" selectionEnd="{{selectionEnd}}" adjustPosition="{{adjustPosition}}" inputAlign="{{inputAlign}}" fontSize="{{fontSize}}" color="{{color}}" prefixIcon="{{prefixIcon}}" suffixIcon="{{suffixIcon}}" suffixIconStyle="{{suffixIconStyle}}" prefixIconStyle="{{prefixIconStyle}}" border="{{border}}" readonly="{{readonly}}" shape="{{shape}}" customStyle="{{customStyle}}" formatter="{{formatter}}" ignoreCompositionEvent="{{ignoreCompositionEvent}}" data-event-opts="{{[['^focus',[['$emit',['focus']]]],['^blur',[['e0']]],['^keyboardheightchange',[['$emit',['keyboardheightchange']]]],['^change',[['e1']]],['^input',[['e2']]],['^confirm',[['e3']]],['^clear',[['$emit',['clear']]]],['^click',[['$emit',['click']]]]]}}" bind:focus="__e" bind:blur="__e" bind:keyboardheightchange="__e" bind:change="__e" bind:input="__e" bind:confirm="__e" bind:clear="__e" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}"><slot name="prefix"></slot><slot name="suffix"></slot></uv-input>