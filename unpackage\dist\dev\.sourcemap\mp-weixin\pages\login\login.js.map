{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/project/vt-unih5-order/pages/login/login.vue?9a06", "webpack:///D:/project/vt-unih5-order/pages/login/login.vue?0d3b", "webpack:///D:/project/vt-unih5-order/pages/login/login.vue?04b7", "webpack:///D:/project/vt-unih5-order/pages/login/login.vue?987f", "uni-app:///pages/login/login.vue", "webpack:///D:/project/vt-unih5-order/pages/login/login.vue?6b65", "webpack:///D:/project/vt-unih5-order/pages/login/login.vue?8fdd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "form", "phone", "isLoading", "isInputFocused", "rules", "required", "message", "trigger", "pattern", "inputStyle", "fontSize", "padding", "backgroundColor", "borderRadius", "primaryButtonStyle", "background", "height", "wechatButtonStyle", "computed", "isValidPhone", "canLogin", "loginButtonText", "methods", "handlePhoneLogin", "console", "handleGetuserinfo", "getPhoneNumber", "uni", "title", "onInputFocus", "onInputBlur", "wxlogin", "complete", "wxToken", "code", "then", "success", "setTimeout", "catch"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0GtnB;EACAC;IACA;MACAC;QACAC;MACA;MACAC;MACAC;MACAC;QACAH,QACA;UACAI;UACAC;UACAC;QACA,GACA;UACAC;UACAF;UACAC;QACA;MAEA;MACAE;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAF;QACAG;QACAN;MACA;MACAO;QACAJ;QACAG;QACAN;MACA;IACA;EACA;EACAQ;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MAEA;QACA;UACA;UACA;QACA;MACA;QACAC;MACA;IACA;IAEA;IACAC;MACAD;MACA;MACA;IACA;IAEA;IACAE;MAAA;MACAF;MACA;MACA;QACAG;UACAC;QACA;QACA;UACAD;UACA;UACA;YACAC;UACA;QACA;UACAD;UACA;YACAC;UACA;QACA;MACA;QACA;UACAA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACAJ;QACAK;UACA,cACAC;YACAC;YACAjC;UACA,GACAkC;YACA;YACA;YACA;cACAP;cACAQ;gBACAC;kBACA;gBACA;cACA;YACA;UACA,GACAC;YACA;YACA;cACAV;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3PA;AAAA;AAAA;AAAA;AAAqqC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACAzrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=b237504c&scoped=true&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&id=b237504c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b237504c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/login.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=b237504c&scoped=true&\"", "var components\ntry {\n  components = {\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form/u-form\" */ \"@/uni_modules/uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"login-container\">\r\n\t\t<!-- 登录卡片 -->\r\n\t\t<view class=\"login-card\">\r\n\t\t\t<!-- Logo和标题 -->\r\n\t\t\t<view class=\"header-section\">\r\n\t\t\t\t<view class=\"title\">登录</view>\r\n\t\t\t\t<view class=\"subtitle\">请输入手机号或使用微信快捷登录</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 表单区域 -->\r\n\t\t\t<view class=\"form-section\">\r\n\t\t\t\t<u-form :model=\"form\" ref=\"uForm\" :rules=\"rules\">\r\n\t\t\t\t\t<u-form-item prop=\"phone\" borderBottom>\r\n\t\t\t\t\t\t<u-input\r\n\t\t\t\t\t\t\tv-model=\"form.phone\"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入手机号\"\r\n\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\tmaxlength=\"11\"\r\n\t\t\t\t\t\t\tprefixIcon=\"phone\"\r\n\t\t\t\t\t\t\tclearable\r\n\t\t\t\t\t\t\t:customStyle=\"inputStyle\"\r\n\t\t\t\t\t\t\t@focus=\"onInputFocus\"\r\n\t\t\t\t\t\t\t@blur=\"onInputBlur\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<template #suffix>\r\n\t\t\t\t\t\t\t\t<u-button\r\n\t\t\t\t\t\t\t\t\tv-if=\"form.phone && !isValidPhone\"\r\n\t\t\t\t\t\t\t\t\ttext=\"格式错误\"\r\n\t\t\t\t\t\t\t\t\ttype=\"error\"\r\n\t\t\t\t\t\t\t\t\tsize=\"mini\"\r\n\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t></u-button>\r\n\t\t\t\t\t\t\t\t<u-button\r\n\t\t\t\t\t\t\t\t\tv-else-if=\"form.phone && isValidPhone\"\r\n\t\t\t\t\t\t\t\t\ttext=\"格式正确\"\r\n\t\t\t\t\t\t\t\t\ttype=\"success\"\r\n\t\t\t\t\t\t\t\t\tsize=\"mini\"\r\n\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t></u-button>\r\n\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t</u-input>\r\n\t\t\t\t\t</u-form-item>\r\n\t\t\t\t</u-form>\r\n\r\n\t\t\t\t<!-- 登录按钮 -->\r\n\t\t\t\t<view class=\"button-section\">\r\n\t\t\t\t\t<u-button\r\n\t\t\t\t\t\t:text=\"loginButtonText\"\r\n\t\t\t\t\t\ttype=\"primary\"\r\n\t\t\t\t\t\tsize=\"large\"\r\n\t\t\t\t\t\t:loading=\"isLoading\"\r\n\t\t\t\t\t\t:disabled=\"!canLogin\"\r\n\t\t\t\t\t\t@click=\"handlePhoneLogin\"\r\n\t\t\t\t\t\t:customStyle=\"primaryButtonStyle\"\r\n\t\t\t\t\t></u-button>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 分割线 -->\r\n\t\t\t\t<view class=\"divider-section\">\r\n\t\t\t\t\t<view class=\"divider-line\"></view>\r\n\t\t\t\t\t<view class=\"divider-text\">或</view>\r\n\t\t\t\t\t<view class=\"divider-line\"></view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 微信登录区域 -->\r\n\t\t\t\t<view class=\"wechat-section\">\r\n\t\t\t\t\t<!-- <u-button\r\n\t\t\t\t\t\ttext=\"微信一键登录\"\r\n\t\t\t\t\t\ttype=\"success\"\r\n\t\t\t\t\t\tsize=\"large\"\r\n\t\t\t\t\t\topen-type=\"getUserInfo\"\r\n\t\t\t\t\t\t@getuserinfo=\"handleGetuserinfo\"\r\n\t\t\t\t\t\t:customStyle=\"wechatButtonStyle\"\r\n\t\t\t\t\t\ticon=\"weixin-fill\"\r\n\t\t\t\t\t></u-button> -->\r\n\r\n\t\t\t\t\t<!-- 快捷获取手机号 -->\r\n\t\t\t\t\t<view class=\"quick-phone-section\" v-if=\"!form.phone\">\r\n\t\t\t\t\t\t<!-- <view class=\"quick-phone-text\">还没有手机号？</view> -->\r\n\t\t\t\t\t\t<u-button\r\n\t\t\t\t\t\t\ttext=\"微信授权获取\"\r\n\t\t\t\t\t\t\ttype=\"info\"\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\topen-type=\"getPhoneNumber\"\r\n\t\t\t\t\t\t\t@getphonenumber=\"getPhoneNumber\"\r\n\t\t\t\t\t\t\tplain\r\n\t\t\t\t\t\t></u-button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 底部提示 -->\r\n\t\t<view class=\"footer-section\">\r\n\t\t\t<view class=\"privacy-text\">\r\n\t\t\t\t登录即表示同意\r\n\t\t\t\t<text class=\"link-text\">《用户协议》</text>\r\n\t\t\t\t和\r\n\t\t\t\t<text class=\"link-text\">《隐私政策》</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tform: {\r\n\t\t\t\t\tphone: ''\r\n\t\t\t\t},\r\n\t\t\t\tisLoading: false,\r\n\t\t\t\tisInputFocused: false,\r\n\t\t\t\trules: {\r\n\t\t\t\t\tphone: [\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\t\tmessage: '请输入手机号',\r\n\t\t\t\t\t\t\ttrigger: ['blur', 'change']\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tpattern: /^1[3-9]\\d{9}$/,\r\n\t\t\t\t\t\t\tmessage: '手机号格式不正确',\r\n\t\t\t\t\t\t\ttrigger: ['blur', 'change']\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t]\r\n\t\t\t\t},\r\n\t\t\t\tinputStyle: {\r\n\t\t\t\t\tfontSize: '16px',\r\n\t\t\t\t\tpadding: '16px 0',\r\n\t\t\t\t\tbackgroundColor: '#f8f9fa',\r\n\t\t\t\t\tborderRadius: '8px'\r\n\t\t\t\t},\r\n\t\t\t\tprimaryButtonStyle: {\r\n\t\t\t\t\tbackground: '#667eea',\r\n\t\t\t\t\tborderRadius: '8px',\r\n\t\t\t\t\theight: '48px',\r\n\t\t\t\t\tfontSize: '16px'\r\n\t\t\t\t},\r\n\t\t\t\twechatButtonStyle: {\r\n\t\t\t\t\tborderRadius: '8px',\r\n\t\t\t\t\theight: '48px',\r\n\t\t\t\t\tfontSize: '16px'\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tisValidPhone() {\r\n\t\t\t\treturn /^1[3-9]\\d{9}$/.test(this.form.phone);\r\n\t\t\t},\r\n\t\t\tcanLogin() {\r\n\t\t\t\treturn this.form.phone && this.isValidPhone && !this.isLoading;\r\n\t\t\t},\r\n\t\t\tloginButtonText() {\r\n\t\t\t\tif (this.isLoading) return '登录中...';\r\n\t\t\t\tif (!this.form.phone) return '请输入手机号';\r\n\t\t\t\tif (!this.isValidPhone) return '手机号格式错误';\r\n\t\t\t\treturn '手机号登录';\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 手机号登录\r\n\t\t\thandlePhoneLogin() {\r\n\t\t\t\tif (!this.canLogin) return;\r\n\r\n\t\t\t\tthis.$refs.uForm.validate().then(valid => {\r\n\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\tthis.isLoading = true;\r\n\t\t\t\t\t\tthis.wxlogin();\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(errors => {\r\n\t\t\t\t\tconsole.log('表单验证失败：', errors);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 微信授权登录\r\n\t\t\thandleGetuserinfo(e) {\r\n\t\t\t\tconsole.log('微信用户信息：', e);\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\tthis.wxlogin();\r\n\t\t\t},\r\n\r\n\t\t\t// 获取微信手机号\r\n\t\t\tgetPhoneNumber(e) {\r\n\t\t\t\tconsole.log('获取手机号：', e);\r\n\t\t\t\tconst { code } = e.detail;\r\n\t\t\t\tif (code) {\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '获取中...'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.$u.api.userPhone(code).then(res => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthis.form.phone = res.data.phoneNumber;\r\n\t\t\t\t\t\tthis.$u.func.showToast({\r\n\t\t\t\t\t\t\ttitle: '手机号获取成功'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthis.$u.func.showToast({\r\n\t\t\t\t\t\t\ttitle: '获取手机号失败'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$u.func.showToast({\r\n\t\t\t\t\t\ttitle: '用户取消授权'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 输入框聚焦事件\r\n\t\t\tonInputFocus() {\r\n\t\t\t\tthis.isInputFocused = true;\r\n\t\t\t},\r\n\r\n\t\t\t// 输入框失焦事件\r\n\t\t\tonInputBlur() {\r\n\t\t\t\tthis.isInputFocused = false;\r\n\t\t\t},\r\n\r\n\t\t\t// 微信登录逻辑\r\n\t\t\twxlogin() {\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\tcomplete: (res) => {\r\n\t\t\t\t\t\tthis.$u.api\r\n\t\t\t\t\t\t\t.wxToken({\r\n\t\t\t\t\t\t\t\tcode: res.code,\r\n\t\t\t\t\t\t\t\tphone: this.form.phone\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.then(data => {\r\n\t\t\t\t\t\t\t\tthis.$u.func.login(data);\r\n\t\t\t\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\t\t\t\tthis.$u.func.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\t\tthis.$u.func.redirect('/pages/index/index');\r\n\t\t\t\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\t\t\t\tthis.$u.func.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: err || '登录失败'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.login-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground: #f8f9fa;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tpadding: 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t// 登录卡片\r\n\t.login-card {\r\n\t\tbackground: #ffffff;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 60rpx 40rpx;\r\n\t\twidth: 100%;\r\n\t\tmax-width: 600rpx;\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\r\n\t}\r\n\r\n\t// 头部区域\r\n\t.header-section {\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 50rpx;\r\n\r\n\t\t.title {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: #333;\r\n\t\t\tmargin-bottom: 12rpx;\r\n\t\t}\r\n\r\n\t\t.subtitle {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tline-height: 1.4;\r\n\t\t}\r\n\t}\r\n\r\n\t// 表单区域\r\n\t.form-section {\r\n\t\t.button-section {\r\n\t\t\tmargin: 32rpx 0;\r\n\t\t}\r\n\r\n\t\t// 分割线\r\n\t\t.divider-section {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin: 32rpx 0;\r\n\r\n\t\t\t.divider-line {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\theight: 1rpx;\r\n\t\t\t\tbackground: #e5e5e5;\r\n\t\t\t}\r\n\r\n\t\t\t.divider-text {\r\n\t\t\t\tmargin: 0 24rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// 微信登录区域\r\n\t\t.wechat-section {\r\n\t\t\t.quick-phone-section {\r\n\t\t\t\tmargin-top: 24rpx;\r\n\t\t\t\ttext-align: center;\r\n\r\n\t\t\t\t.quick-phone-text {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\tmargin-bottom: 12rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// 底部区域\r\n\t.footer-section {\r\n\t\tposition: absolute;\r\n\t\tbottom: 60rpx;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\r\n\t\t.privacy-text {\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tcolor: #999;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 1.5;\r\n\r\n\t\t\t.link-text {\r\n\t\t\t\tcolor: #667eea;\r\n\t\t\t\ttext-decoration: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// 响应式适配\r\n\t@media screen and (max-height: 800px) {\r\n\t\t.login-container {\r\n\t\t\tpadding: 20rpx;\r\n\t\t}\r\n\r\n\t\t.login-card {\r\n\t\t\tpadding: 40rpx 30rpx;\r\n\t\t}\r\n\r\n\t\t.footer-section {\r\n\t\t\tbottom: 30rpx;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=b237504c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=b237504c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759027613569\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}