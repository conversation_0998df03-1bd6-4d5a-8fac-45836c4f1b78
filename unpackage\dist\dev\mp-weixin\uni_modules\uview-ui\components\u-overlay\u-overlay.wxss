@charset "UTF-8";
/* 水平间距 */
/* 水平间距 */
view.data-v-1625041b, scroll-view.data-v-1625041b, swiper-item.data-v-1625041b {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-overlay.data-v-1625041b {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
}

