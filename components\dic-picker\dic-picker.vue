<template>
  <view class="">
    <view
      @click="show = !show"
      style="display: flex; justify-content: space-between; align-items: center"
    >
      <view style="text-align: right; flex: 1">
        {{ text || placeholder }}
      </view>
      <u-icon slot="right" name="arrow-right"></u-icon>
    </view>
    <u-picker
      closeOnClickOverlay
      :show="show"
      @close="show = false"
      @confirm="confirm"
      @cancel="show = false"
      :columns="columns"
    ></u-picker>
  </view>
</template>

<script>
export default {
  name: "dic-picker",
  emit: ["input"],
  props: {
    dicUrl: String,
    value: String,
    props: {
      type: Object,
      default: () => {
        return {
          label: "dictValue",
          value: "id",
        };
      },
    },
    placeholder: String,
  },
  data() {
    return {
      show: false,
      columns: [],
      dicData: [],
      text: "",
    };
  },
  watch: {
    dicUrl: {
      handler(nv, ov) {
        if (nv) {
          this.getList();
        }
      },
      immediate: true,
    },
    value: {
      handler(nv, ov) {
        if (nv && !this.text) {
          this.renderText();
        }
      },
    },
  },
  methods: {
    confirm(val) {
      this.text = val.value[0];
      const value = this.dicData[val.indexs[0]].id;
      this.$emit("input", value);
      this.show = false;
    },
    cancel() {
      this.show = false;
    },
    getList() {
      this.$u.api.getdictListByUrl(this.dicUrl).then((res) => {
        this.dicData = res.data;
        this.columns = [res.data.map((item) => item.dictValue)];
        if (this.value) {
          this.renderText();
        }
      });
    },
    renderText() {
      this.text =
        this.dicData.find((item) => item.id == this.value)?.dictValue ||
        this.value;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-input__content__field-wrapper__field {
  text-align: right !important;
}

/* Ensure placeholder is also right-aligned for various browsers and uView internal input */
::v-deep .u-input__content__field-wrapper__field::placeholder {
  text-align: right !important;
}
::v-deep .u-input__content__field-wrapper__field::-webkit-input-placeholder {
  text-align: right !important;
}
::v-deep .u-input__content__field-wrapper__field:-ms-input-placeholder {
  text-align: right !important;
}

/* Also target possible alternative internal classes in different uView versions */
::v-deep .u-input__content__field-wrapper,
::v-deep .u-input__content__field-wrapper input,
::v-deep input.u-input__field {
  text-align: right !important;
}

/* If input is disabled, some browsers change color/opacity - keep placeholder visible */
::v-deep .u-input__content__field-wrapper__field[disabled]::placeholder {
  text-align: right !important;
  opacity: 1 !important;
}
</style>
