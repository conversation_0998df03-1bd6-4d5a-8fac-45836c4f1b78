import {
	clientId,
	clientSecret,
} from '@/common/setting'
import {
	options
} from '@/http/config.js';
import {
	Base64
} from '@/utils/base64.js';
import Request from '@/utils/luch-request/index.js';
const http = new Request(options);

http.interceptors.request.use((config) => { // 可使用async await 做异步操作
	// 假设有token值需要在头部需要携带
	let accessToken = uni.getStorageSync('accessToken');
	if (accessToken) {
		config.header['Blade-Auth'] = 'bearer ' + accessToken;
	}
	config.header["Tenant-Id"] = "806174";
	// 客户端认证参数
	config.header['Authorization'] = 'Basic ' + Base64.encode(clientId + ':' + clientSecret);
	// // #ifdef MP
	// 	console.log(config.url)
	// 	if(config.url.indexOf('blade') < 0){
	// 		config.url = config.url.replace('ade','blade')
	// 	}
	// // #endif
	// // #ifndef H5
	// let url = config.url
	// if (process.env.NODE_ENV == 'development' && !url.startsWith("http")) {
	// 	url = url.substring(url.indexOf('/api') + 4)
	// 	config.url = url
	// }
	// // #endif

	// 额外参数
	// config.data = config.data || {};
	// config.data.pf = uni.getSystemInfoSync().platform;
	// config.data.sys = uni.getSystemInfoSync().system;

	// 演示custom 用处
	// if (config.custom.auth) {
	//   config.header.token = 'token'
	// }
	// if (config.custom.loading) {
	//  uni.showLoading()
	// }
	/**
	 /* 演示
	 if (!token) { // 如果token不存在，return Promise.reject(config) 会取消本次请求
	    return Promise.reject(config)
	  }
	 **/
	uni.showLoading({
		title: '加载中'
	})
	return config
}, config => { // 可使用async await 做异步操作
	return Promise.reject(config)
})
http.interceptors.response.use((response) => {
	// 若有数据返回则通过
	uni.hideLoading()
	if (response.data.access_token || response.data.key) {
		return response.data
	}
	// 服务端返回的状态码不等于200，则reject()
	if (response.data.code !== 200 && response.data.msg != 'success') {
		uni.showToast({
			title: response.data.msg,
			icon: 'none'
		});
		return Promise.reject(response);
	}
	return response.data;
}, (response) => {
	/*  对响应错误做点什么 （statusCode !== 200）*/
	
	uni.showToast({
		title: (response.data && response.data.msg) || response.data && response.data.error_description,
		icon: 'none',
		duration: 2000
	});
	if (response.statusCode == 400) {
		const pages = getCurrentPages()
		const currentPage = pages[pages.length - 1]
		uni.redirectTo({
			url: `/pages/login/login`
		})
	}
	return Promise.reject(response)
})
export default http;