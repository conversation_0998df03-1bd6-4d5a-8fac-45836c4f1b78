import http from '@/http/api.js'

// 获取首页轮播图
const getIndexBannerList = () => {
	return http.request({
		url: '/vt-admin/itSlideshow/pageForMini',
		method: 'get',
	})
}

// 获取首页轮播图详情
const getIndexBannerDetail = (id) => {
	return http.request({
		url: '/vt-admin/itSlideshow/detail',
		method: 'get',
		params: {
			id
		}
	})
}

const getItServerByType = (type) => {
	return http.request({
		url: '/vt-admin/itServer/detail',
		method: 'get',
		params: {
			type
		}
	})
}
export default {
	getIndexBannerList,
	getItServerByType,
	getIndexBannerDetail
}