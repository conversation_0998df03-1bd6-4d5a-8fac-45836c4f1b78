@charset "UTF-8";
/* 水平间距 */
/* 水平间距 */
.uni-row {
  position: relative;
  flex-direction: row;
  box-sizing: border-box;
}
.uni-row::before,
.uni-row::after {
  display: table;
  content: "";
}
.uni-row::after {
  clear: both;
}
.uni-row--flex {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  flex: 1;
}
.uni-row--flex:before, .uni-row--flex:after {
  display: none;
}
.uni-row--flex-justify-center {
  justify-content: center;
}
.uni-row--flex-justify-end {
  justify-content: flex-end;
}
.uni-row--flex-justify-space-between {
  justify-content: space-between;
}
.uni-row--flex-justify-space-around {
  justify-content: space-around;
}
.uni-row--flex-align-middle {
  align-items: center;
}
.uni-row--flex-align-bottom {
  align-items: flex-end;
}
:host {
  display: block;
}

