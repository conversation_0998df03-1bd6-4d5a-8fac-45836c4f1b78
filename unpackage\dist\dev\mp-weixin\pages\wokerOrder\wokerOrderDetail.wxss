
.order-detail.data-v-41762ff7 {
  padding: 24rpx;
  background: linear-gradient(180deg, #f8faff 0%, #f0f4ff 100%);
  min-height: 100vh;
}
.card.data-v-41762ff7 {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  border: 1rpx solid #e8f0fe;
  box-shadow: 0 8rpx 24rpx rgba(41, 121, 255, 0.08);
  transition: all 0.3s ease;
}
.shadow.data-v-41762ff7:hover {
  box-shadow: 0 12rpx 32rpx rgba(41, 121, 255, 0.12);
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
}
.card-header.data-v-41762ff7 {
  display: flex;
  align-items: center;
  padding: 28rpx 24rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #fff 100%);
  position: relative;
}
.card-header.data-v-41762ff7::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.7) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
}
.header-icon.data-v-41762ff7 {
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}
.card-title.data-v-41762ff7 {
  font-size: 32rpx;
  font-weight: 600;
  color: #000;
  letter-spacing: 0.5rpx;
}
.card-content.data-v-41762ff7 {
  padding: 32rpx 24rpx;
}
.info-row.data-v-41762ff7 {
  display: flex;
  margin-bottom: 24rpx;
  gap: 24rpx;
}
.info-row.single.data-v-41762ff7 {
  margin-bottom: 20rpx;
}
.info-row.data-v-41762ff7:last-child {
  margin-bottom: 0;
}
.info-item.data-v-41762ff7 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.info-item.full.data-v-41762ff7 {
  flex: 1;
}
.label.data-v-41762ff7 {
  font-size: 24rpx;
  color: #8a8a8a;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}
.value.data-v-41762ff7 {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
}
.value.important.data-v-41762ff7 {
  color: #2979ff;
  font-weight: 600;
}
.value.tag.data-v-41762ff7 {
  background: #e3f2fd;
  color: #1976d2;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  align-self: flex-start;
}
.value.time.data-v-41762ff7 {
  color: #ff6b35;
  font-weight: 500;
}
.value.phone.data-v-41762ff7 {
  color: #2979ff;
  text-decoration: underline;
}
.value.satisfy.data-v-41762ff7 {
  color: #4caf50;
  font-weight: 600;
  background: #e8f5e8;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  align-self: flex-start;
}
.value.code.data-v-41762ff7 {
  font-family: "Courier New", monospace;
  background: #f5f5f5;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
}
.value.desc.data-v-41762ff7 {
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #2979ff;
}
.value.file-link.data-v-41762ff7 {
  color: #2979ff;
  text-decoration: underline;
  background: #e3f2fd;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  align-self: flex-start;
}
.timeline-container.data-v-41762ff7 {
  padding: 16rpx;
}
.timeline.data-v-41762ff7 {
  position: relative;
  padding-left: 48rpx;
}
.timeline-item.data-v-41762ff7 {
  position: relative;
  padding-bottom: 32rpx;
}
.timeline-item.data-v-41762ff7:last-child {
  padding-bottom: 0;
}
.timeline-dot.data-v-41762ff7 {
  position: absolute;
  left: -54rpx;
  top: 12rpx;
  width: 20rpx;
  height: 20rpx;
  background: #e0e0e0;
  border-radius: 50%;
  border: 4rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 2;
}
.timeline-dot.active.data-v-41762ff7 {
  background: #2979ff;
  box-shadow: 0 0 0 4rpx rgba(41, 121, 255, 0.2);
}
.timeline-line.data-v-41762ff7 {
  position: absolute;
  left: -46rpx;
  top: 36rpx;
  bottom: -32rpx;
  width: 4rpx;
  background: linear-gradient(180deg, #2979ff 0%, #e0e0e0 100%);
  border-radius: 2rpx;
}
.timeline-item:last-child .timeline-line.data-v-41762ff7 {
  display: none;
}
.timeline-content.data-v-41762ff7 {
  background: white;
  padding: 24rpx;
  border-radius: 16rpx;
  border: 1rpx solid #e8f0fe;
  box-shadow: 0 4rpx 12rpx rgba(41, 121, 255, 0.05);
  position: relative;
}
.timeline-content.data-v-41762ff7::before {
  content: "";
  position: absolute;
  left: -12rpx;
  top: 20rpx;
  width: 0;
  height: 0;
  border-top: 8rpx solid transparent;
  border-bottom: 8rpx solid transparent;
  border-right: 12rpx solid white;
}
.timeline-header.data-v-41762ff7 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.timeline-title.data-v-41762ff7 {
  font-size: 28rpx;
  font-weight: 600;
  color: #2979ff;
}
.timeline-time.data-v-41762ff7 {
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}
.timeline-desc.data-v-41762ff7 {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}
.timeline-operator.data-v-41762ff7 {
  font-size: 24rpx;
  color: #999;
  font-style: italic;
}

/* 底部占位空间 */
.bottom-placeholder.data-v-41762ff7 {
  height: 240rpx; /* 为固定底部操作栏留出空间 */
}

/* 固定底部操作栏 */
.fixed-action-bar.data-v-41762ff7 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom)); /* 适配安全区域 */
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  border-top: 1rpx solid #f0f0f0;
  z-index: 999;
}
.action-buttons.data-v-41762ff7 {
  display: flex;
  gap: 24rpx;
  justify-content: flex-end;
  align-items: center;
}
.action-btn.data-v-41762ff7 {
  min-width: 120rpx;
  padding: 0 32rpx;
  height: 72rpx;
  line-height: 72rpx;
  border: none;
  border-radius: 36rpx;
  background: #f5f7fa;
  color: #2979ff;
  font-size: 28rpx;
  font-weight: 500;
  margin: 0;
  outline: none;
  box-shadow: 0 4rpx 12rpx 0 rgba(45, 140, 240, 0.15);
  transition: all 0.3s ease;
}
.action-btn.primary.data-v-41762ff7 {
  background: linear-gradient(90deg, #2979ff 0%, #57a3f3 100%);
  color: #fff;
  box-shadow: 0 4rpx 16rpx 0 rgba(45, 140, 240, 0.3);
}
.action-btn.data-v-41762ff7:active {
  opacity: 0.85;
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
}

/* 完成弹窗样式 */
.finish-popup.data-v-41762ff7 {
  height: 80vh;
  overflow-y: auto;
  position: relative;
}
.popup-title.data-v-41762ff7 {
  padding-top: 20rpx;
  position: -webkit-sticky;
  position: sticky;
  background-color: #fff;
  top: 0;
  z-index: 10;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
}
.popup-content.data-v-41762ff7 {
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.popup-footer.data-v-41762ff7 {
  padding: 20rpx 0;
  position: fixed;
  bottom: 0;
  left: 20rpx;
  right: 20rpx;
  width: auto;
}

/* 输入框右对齐 */
.data-v-41762ff7 input {
  text-align: right !important;
}

/* 完工报告样式 */
.completion-report.data-v-41762ff7 {
  position: relative;
  margin-bottom: 32rpx;
}
.report-header.data-v-41762ff7 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, #f8faff 0%, #e3f2fd 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #2979ff;
}
.report-title.data-v-41762ff7 {
  font-size: 32rpx;
  font-weight: 600;
  color: #2979ff;
}
.report-time.data-v-41762ff7 {
  font-size: 24rpx;
  color: #666;
  background: rgba(255, 255, 255, 0.8);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

/* 折叠面板样式 */
.collapsible-section.data-v-41762ff7 {
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 1rpx solid #e8f0fe;
}
.section-header.data-v-41762ff7 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8faff;
  cursor: pointer;
  transition: all 0.3s ease;
}
.section-header.data-v-41762ff7:active {
  background: #e3f2fd;
}
.section-title.data-v-41762ff7 {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.section-text.data-v-41762ff7 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.section-arrow.data-v-41762ff7 {
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.section-arrow.expanded.data-v-41762ff7 {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.section-content.data-v-41762ff7 {
  padding: 20rpx;
  background: #fff;
  border-top: 1rpx solid #e8f0fe;
}
.report-divider.data-v-41762ff7 {
  height: 1rpx;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #e0e0e0 50%,
    transparent 100%
  );
  margin: 32rpx 0;
}

/* 完成状态标签样式 */
.value.tag.status-complete.data-v-41762ff7 {
  background: #e8f5e8;
  color: #4caf50;
}
.value.tag.status-partial.data-v-41762ff7 {
  background: #fff3e0;
  color: #ff9800;
}
.value.tag.status-incomplete.data-v-41762ff7 {
  background: #ffebee;
  color: #f44336;
}

/* 图片网格样式 */
.photo-grid.data-v-41762ff7 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 12rpx;
}
.photo-item.data-v-41762ff7 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 1rpx solid #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
}
.photo-item.data-v-41762ff7:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  opacity: 0.8;
}
.photo-image.data-v-41762ff7 {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

