@charset "UTF-8";
/* 水平间距 */
/* 水平间距 */
.uv-safe-bottom.data-v-a312b79e {
  width: 100%;
}
.uv-safe-area-inset-top.data-v-a312b79e {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.uv-safe-area-inset-right.data-v-a312b79e {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.uv-safe-area-inset-bottom.data-v-a312b79e {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.uv-safe-area-inset-left.data-v-a312b79e {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}

