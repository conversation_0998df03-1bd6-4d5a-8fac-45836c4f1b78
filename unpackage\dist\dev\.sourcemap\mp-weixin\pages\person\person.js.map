{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/project/vt-unih5-order/pages/person/person.vue?ce28", "webpack:///D:/project/vt-unih5-order/pages/person/person.vue?4979", "webpack:///D:/project/vt-unih5-order/pages/person/person.vue?bf0c", "webpack:///D:/project/vt-unih5-order/pages/person/person.vue?c098", "uni-app:///pages/person/person.vue", "webpack:///D:/project/vt-unih5-order/pages/person/person.vue?d141", "webpack:///D:/project/vt-unih5-order/pages/person/person.vue?a24c", "webpack:///D:/project/vt-unih5-order/pages/person/person.vue?4ab3", "webpack:///D:/project/vt-unih5-order/pages/person/person.vue?699e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "version", "user_banner", "user_avg", "user_name", "userInfo", "phone", "created", "uni", "success", "_this", "onShow", "methods", "<PERSON><PERSON><PERSON><PERSON>", "url", "getUserDetail", "console", "toUserInfo", "toMyRepair", "toMyOrder", "toMyWokerOrder", "toPayOrder"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;AACC;;;AAGnE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,kRAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,EAAE,IAAI,EAAE,IAAI,EAAE;AACpD;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,gnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCqGvnB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;;IAEA;IACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAL;QACAM;MACA;IACA;IACAC;MAAA;MACA;QACAC;QACA;MACA;IACA;IACAC;MACAT;QACAM;MACA;IACA;IACAI;MACAV;QACAM;MACA;IACA;IACAK;MACAX;QACAM;MACA;IACA;IACAM;MACAZ;QACAM;MACA;IACA;IACAO;MACAb;QACAM;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC5KA;AAAA;AAAA;AAAA;AAAs3B,CAAgB,q2BAAG,EAAC,C;;;;;;;;;;;ACA14B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA8oC,CAAgB,+lCAAG,EAAC,C;;;;;;;;;;;ACAlqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/person/person.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/person/person.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./person.vue?vue&type=template&id=390a77b4&\"\nvar renderjs\nimport script from \"./person.vue?vue&type=script&lang=js&\"\nexport * from \"./person.vue?vue&type=script&lang=js&\"\nimport style0 from \"./person.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./person.vue?vue&type=style&index=1&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/person/person.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./person.vue?vue&type=template&id=390a77b4&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-avatar/u-avatar\" */ \"@/uni_modules/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uniSection: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-section/components/uni-section/uni-section\" */ \"@/uni_modules/uni-section/components/uni-section/uni-section.vue\"\n      )\n    },\n    uniRow: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-row/components/uni-row/uni-row\" */ \"@/uni_modules/uni-row/components/uni-row/uni-row.vue\"\n      )\n    },\n    uniCol: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-row/components/uni-col/uni-col\" */ \"@/uni_modules/uni-row/components/uni-col/uni-col.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.userInfo.phone\n    ? _vm.userInfo.phone.replace(/(\\d{3})\\d{4}(\\d{4})/, \"$1****$2\")\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./person.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./person.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"user\">\r\n\t\t<!-- 底图 - 开始 -->\r\n\t\t<!-- <image class=\"user-header-image\" :src=\"user_banner\"></image> -->\r\n\t\t<!-- 底图 - 结束 -->\r\n\t\t<!-- 上方渐变背景 -->\r\n\t\t<view class=\"bg\">\r\n\r\n\t\t</view>\r\n\t\t<!-- 用户信息 - 开始 -->\r\n\t\t<view class=\"user-info-box\">\r\n\t\t\t<u-avatar :src=\"userInfo.avatar?userInfo.avatar:user_avg\" shape=\"square\"></u-avatar>\r\n\t\t\t<view @click=\"toLogin()\" class=\"user-info-right\">\r\n\t\t\t\t<view class=\"user-nickname\">{{ userInfo.name?userInfo.name:user_name }}</view>\r\n\t\t\t\t<view class=\"user-phone\">\r\n\t\t\t\t\t{{ userInfo.phone?userInfo.phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2') : '' }}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 用户信息 - 结束 -->\r\n\r\n\r\n\r\n\t\t<!-- 列表菜单 - 开始 -->\r\n\t\t<view class=\"user-activity-menu\">\r\n\t\t\t<!-- <view class=\"menu-item\" @click=\"goPage('coupon')\">\r\n\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t<image class=\"menu-icon\" src=\"../../static/icon/icon_coupon.png\"></image>\r\n\t\t\t\t\t<view class=\"menu-name\">我的代金券</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-icon name=\"arrow-right\"></u-icon>\r\n\t\t\t</view> -->\r\n\t\t\t<!-- <view class=\"menu-item\" @click=\"goPage('orderList')\">\r\n\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t<image class=\"menu-icon\" src=\"../../static/icon/icon_order.png\"></image>\r\n\t\t\t\t\t<view class=\"menu-name\">我的订单</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-icon name=\"arrow-right\"></u-icon>\r\n\t\t\t</view> -->\r\n\t\t\t<view @click=\"toUserInfo\" class=\"menu-item\">\r\n\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t<image class=\"menu-icon\" src=\"../../static/icon/icon_userinfo.png\"></image>\r\n\t\t\t\t\t<view class=\"menu-name\">个人资料</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-icon name=\"arrow-right\"></u-icon>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"menu-item\" @click=\"goPage('contact')\">\r\n\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t<image class=\"menu-icon\" src=\"../../static/icon/icon_contact.png\"></image>\r\n\t\t\t\t\t<view class=\"menu-name\">联系我们</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-icon name=\"arrow-right\"></u-icon>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view @click=\"toMyOrder\" class=\"menu-item\">\r\n\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t<image class=\"menu-icon\" src=\"../../static/icon/icon_about.png\"></image>\r\n\t\t\t\t\t<view class=\"menu-name\">我的订单</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-icon name=\"arrow-right\"></u-icon>\r\n\t\t\t</view> -->\r\n\t\t\t<view @click=\"toMyRepair\" class=\"menu-item\">\r\n\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t<image class=\"menu-icon\" src=\"../../static/icon/icon_about.png\"></image>\r\n\t\t\t\t\t<view class=\"menu-name\">我的报修单</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-icon name=\"arrow-right\"></u-icon>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view @click=\"toMyWokerOrder\" class=\"menu-item\">\r\n\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t<image class=\"menu-icon\" src=\"../../static/icon/icon_about.png\"></image>\r\n\t\t\t\t\t<view class=\"menu-name\">我的工单</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-icon name=\"arrow-right\"></u-icon>\r\n\t\t\t</view> -->\r\n\t\t</view>\r\n\t\t<!-- 列表菜单 - 结束 -->\r\n\t\t<view class=\"other-info-box\" style=\"margin-top: 20rpx;\">\r\n\t\t\t<uni-section title=\"其它功能\" style=\"border-radius: 20rpx;\" padding=\"0 20rpx 20rpx 20rpx\" type=\"line\">\r\n\t\t\t\t<uni-row style=\"width: 100%;\">\r\n\t\t\t\t\t<uni-col :span=\"6\">\r\n\t\t\t\t\t\t<view class=\"\" @click=\"toMyWokerOrder\"\r\n\t\t\t\t\t\t\tstyle=\"display: flex;flex-direction: column;align-items: center;justify-content: center;width: 100%;color:#333\">\r\n\t\t\t\t\t\t\t<image style=\"height: 60rpx;\" src=\"../../static/img/pay.png\" mode=\"heightFix\"></image>\r\n\t\t\t\t\t\t\t工单中心\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</uni-col>\r\n\t\t\t\t\t<!-- <uni-col :span=\"6\">\r\n\t\t\t\t\t\t<view class=\"\"\r\n\t\t\t\t\t\t\tstyle=\"display: flex;flex-direction: column;align-items: center;justify-content: center;width: 100%;color:#333\">\r\n\t\t\t\t\t\t\t<image style=\"height: 60rpx;\" src=\"../../static/img/payRecord.png\" mode=\"heightFix\"></image>\r\n\t\t\t\t\t\t\t付款记录\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</uni-col> -->\r\n\t\t\t\t</uni-row>\r\n\t\t\t</uni-section>\r\n\t\t</view>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tversion: '1.0.0',\r\n\t\t\t\tuser_banner: '../../static/index_banner.png',\r\n\t\t\t\tuser_avg: '../../static/logo.png',\r\n\t\t\t\tuser_name: '点击登录/注册',\r\n\t\t\t\tuserInfo: {\r\n\t\t\t\t\tphone: ''\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tlet _this = this\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t_this.version = res.appVersion\r\n\t\t\t\t},\r\n\t\t\t})\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tconst store = uni.getStorageSync('system')\r\n\t\t\t// this.user_avg = store[5].content\r\n\t\t\t// this.user_name = store[6].content\r\n\t\t\t// this.user_banner = store[3].content\r\n\r\n\t\t\tconst userInfo = uni.getStorageSync('userInfo')\r\n\t\t\tif (userInfo.user_id) {\r\n\t\t\t\tthis.getUserDetail()\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttoLogin() {\r\n\t\t\t\tif (this.userInfo.id) return\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetUserDetail() {\r\n\t\t\t\tthis.$u.api.userInfo().then(res => {\r\n\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\tthis.userInfo = res.data\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoUserInfo() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/person/userInfo'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoMyRepair() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/repair/repair'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoMyOrder() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/order/order'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoMyWokerOrder() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/wokerOrder/wokerOrder'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoPayOrder() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/pay/payOrder'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\tpage {\r\n\t\tbackground: #F4F4F4;\r\n\t}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n\t.user {\r\n\t\t.user-header-image {\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\r\n\t\t.user-info-box {\r\n\t\t\tbackground: #ffffff;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tmargin: -80rpx 20rpx 20rpx 20rpx;\r\n\t\t\tposition: relative;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t.user-avg {\r\n\t\t\t\twidth: 100rpx;\r\n\t\t\t\theight: 100rpx;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.user-nickname {\r\n\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.user-phone {\r\n\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #666666;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.user-recharge-wrapper {\r\n\t\t\tbackground: #ffffff;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tmargin: 20rpx;\r\n\r\n\t\t\t.user-recharge-box {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t\t\t.recharge-info {\r\n\t\t\t\t\t.info-title {\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.info-content {\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.recharge-button {\r\n\t\t\t\t\t// background: #ff4131;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tpadding: 10rpx 20rpx;\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tborder-radius: 50rpx;\r\n\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\tborder: 1rpx solid #dadbde;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.recharge-user-money {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #333;\r\n\r\n\t\t\t\t.recharge-money {\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.recharge-money::first-letter {\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.user-activity-menu {\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tmargin: 20rpx;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tbackground: #fff;\r\n\r\n\t\t\t.menu-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tmargin-top: 60rpx;\r\n\r\n\t\t\t\t.left {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t.menu-icon {\r\n\t\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\t.menu-name {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.menu-number {\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\tletter-spacing: 2rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.menu-item:first-child {\r\n\t\t\t\tmargin-top: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.bg {\r\n\t\theight: 400rpx;\r\n\t\tbackground: linear-gradient(to bottom, #c3cfe2, #fff);\r\n\t}\r\n\r\n\t.other-info-box {\r\n\t\tbackground: #ffffff;\r\n\t\t// padding: 30rpx;\r\n\t\tmargin: -80rpx 20rpx 20rpx 20rpx;\r\n\t\tposition: relative;\r\n\t\tborder-radius: 20rpx;\r\n\t\t// display: flex;\r\n\t\t// align-items: center;\r\n\t}\r\n\r\n\r\n\t::v-deep .uni-section {\r\n\t\tbackground-color: transparent !important;\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./person.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./person.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759027608463\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./person.vue?vue&type=style&index=1&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./person.vue?vue&type=style&index=1&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759027613587\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}