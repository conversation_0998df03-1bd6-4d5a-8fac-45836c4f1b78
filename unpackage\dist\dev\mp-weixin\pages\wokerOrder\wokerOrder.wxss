@charset "UTF-8";
/* 水平间距 */
/* 水平间距 */
.wrap.data-v-0ec3f686 {
  padding: 10rpx;
  box-sizing: border-box;
  height: 100vh;
}
.wrap .search-box.data-v-0ec3f686 {
  height: 68rpx;
}
.wrap .content_box.data-v-0ec3f686 {
  box-sizing: border-box;
  margin-top: 10rpx;
  height: calc(100% - 68rpx - 20rpx - 64rpx);
  color: #000;
}
.wrap .item.data-v-0ec3f686 {
  margin-bottom: 10rpx;
}
.card-actions.data-v-0ec3f686 {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  height: 45px;
  border-top: 1px #eee solid;
}
.card-actions-item.data-v-0ec3f686 {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.card-actions-item-text.data-v-0ec3f686 {
  font-size: 12px;
  color: #666;
  margin-left: 5px;
}
.circle.data-v-0ec3f686 {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid var(--el-color-success);
  color: var(--el-color-success);
  line-height: 50px;
  text-align: center;
  margin-right: 10px;
}
.finish-popup.data-v-0ec3f686 {
  padding: 30rpx;
}
.finish-popup .popup-title.data-v-0ec3f686 {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
}
.finish-popup .popup-content.data-v-0ec3f686 {
  margin-bottom: 30rpx;
}
.finish-popup .popup-footer.data-v-0ec3f686 {
  padding: 20rpx 0;
}

