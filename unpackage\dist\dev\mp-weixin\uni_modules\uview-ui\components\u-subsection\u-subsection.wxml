<view data-ref="u-subsection" class="{{['u-subsection','data-v-78c1286e','vue-ref','u-subsection--'+mode]}}" style="{{$root.s0}}"><view data-ref="u-subsection__bar" class="{{['u-subsection__bar','data-v-78c1286e','vue-ref',mode==='button'&&'u-subsection--button__bar',current===0&&mode==='subsection'&&'u-subsection__bar--first',$root.g0,$root.g1]}}" style="{{$root.s1}}"></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-ref="{{'u-subsection__item--'+index}}" data-event-opts="{{[['tap',[['clickHandler',[index]]]]]}}" class="{{['u-subsection__item','data-v-78c1286e','vue-ref-in-for','u-subsection__item--'+index,item.g2,index===0&&'u-subsection__item--first',item.g3]}}" style="{{item.s2}}" bindtap="__e"><text class="u-subsection__item__text data-v-78c1286e" style="{{item.s3}}">{{item.m0}}</text></view></block></view>