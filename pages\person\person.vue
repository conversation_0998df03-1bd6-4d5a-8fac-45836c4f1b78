<template>
	<view class="user">
		<!-- 底图 - 开始 -->
		<!-- <image class="user-header-image" :src="user_banner"></image> -->
		<!-- 底图 - 结束 -->
		<!-- 上方渐变背景 -->
		<view class="bg">

		</view>
		<!-- 用户信息 - 开始 -->
		<view class="user-info-box">
			<u-avatar :src="userInfo.avatar?userInfo.avatar:user_avg" shape="square"></u-avatar>
			<view @click="toLogin()" class="user-info-right">
				<view class="user-nickname">{{ userInfo.name?userInfo.name:user_name }}</view>
				<view class="user-phone">
					{{ userInfo.phone?userInfo.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '' }}
				</view>
			</view>
		</view>
		<!-- 用户信息 - 结束 -->



		<!-- 列表菜单 - 开始 -->
		<view class="user-activity-menu">
			<!-- <view class="menu-item" @click="goPage('coupon')">
				<view class="left">
					<image class="menu-icon" src="../../static/icon/icon_coupon.png"></image>
					<view class="menu-name">我的代金券</view>
				</view>
				<u-icon name="arrow-right"></u-icon>
			</view> -->
			<!-- <view class="menu-item" @click="goPage('orderList')">
				<view class="left">
					<image class="menu-icon" src="../../static/icon/icon_order.png"></image>
					<view class="menu-name">我的订单</view>
				</view>
				<u-icon name="arrow-right"></u-icon>
			</view> -->
			<view @click="toUserInfo" class="menu-item">
				<view class="left">
					<image class="menu-icon" src="../../static/icon/icon_userinfo.png"></image>
					<view class="menu-name">个人资料</view>
				</view>
				<u-icon name="arrow-right"></u-icon>
			</view>
			<view class="menu-item" @click="goPage('contact')">
				<view class="left">
					<image class="menu-icon" src="../../static/icon/icon_contact.png"></image>
					<view class="menu-name">联系我们</view>
				</view>
				<u-icon name="arrow-right"></u-icon>
			</view>
			<!-- <view @click="toMyOrder" class="menu-item">
				<view class="left">
					<image class="menu-icon" src="../../static/icon/icon_about.png"></image>
					<view class="menu-name">我的订单</view>
				</view>
				<u-icon name="arrow-right"></u-icon>
			</view> -->
			<view @click="toMyRepair" class="menu-item">
				<view class="left">
					<image class="menu-icon" src="../../static/icon/icon_about.png"></image>
					<view class="menu-name">我的报修单</view>
				</view>
				<u-icon name="arrow-right"></u-icon>
			</view>
			<!-- <view @click="toMyWokerOrder" class="menu-item">
				<view class="left">
					<image class="menu-icon" src="../../static/icon/icon_about.png"></image>
					<view class="menu-name">我的工单</view>
				</view>
				<u-icon name="arrow-right"></u-icon>
			</view> -->
		</view>
		<!-- 列表菜单 - 结束 -->
		<view class="other-info-box" style="margin-top: 20rpx;">
			<uni-section title="其它功能" style="border-radius: 20rpx;" padding="0 20rpx 20rpx 20rpx" type="line">
				<uni-row style="width: 100%;">
					<uni-col :span="6">
						<view class="" @click="toMyWokerOrder"
							style="display: flex;flex-direction: column;align-items: center;justify-content: center;width: 100%;color:#333">
							<image style="height: 60rpx;" src="../../static/img/pay.png" mode="heightFix"></image>
							工单中心
						</view>
					</uni-col>
					<!-- <uni-col :span="6">
						<view class=""
							style="display: flex;flex-direction: column;align-items: center;justify-content: center;width: 100%;color:#333">
							<image style="height: 60rpx;" src="../../static/img/payRecord.png" mode="heightFix"></image>
							付款记录
						</view>
					</uni-col> -->
				</uni-row>
			</uni-section>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				version: '1.0.0',
				user_banner: '../../static/index_banner.png',
				user_avg: '../../static/logo.png',
				user_name: '点击登录/注册',
				userInfo: {
					phone: ''
				}
			};
		},
		created() {
			let _this = this
			uni.getSystemInfo({
				success: function(res) {
					_this.version = res.appVersion
				},
			})
		},
		onShow() {
			const store = uni.getStorageSync('system')
			// this.user_avg = store[5].content
			// this.user_name = store[6].content
			// this.user_banner = store[3].content

			const userInfo = uni.getStorageSync('userInfo')
			if (userInfo.user_id) {
				this.getUserDetail()
			}
		},
		methods: {
			toLogin() {
				if (this.userInfo.id) return
				uni.navigateTo({
					url: '/pages/login/login'
				})
			},
			getUserDetail() {
				this.$u.api.userInfo().then(res => {
					console.log(res);
					this.userInfo = res.data
				})
			},
			toUserInfo() {
				uni.navigateTo({
					url: '/pages/person/userInfo'
				})
			},
			toMyRepair() {
				uni.navigateTo({
					url: '/pages/repair/repair'
				})
			},
			toMyOrder() {
				uni.navigateTo({
					url: '/pages/order/order'
				})
			},
			toMyWokerOrder() {
				uni.navigateTo({
					url: '/pages/wokerOrder/wokerOrder'
				})
			},
			toPayOrder() {
				uni.navigateTo({
					url: '/pages/pay/payOrder'
				})
			}

		}
	}
</script>

<style>
	page {
		background: #F4F4F4;
	}
</style>

<style lang="scss">
	.user {
		.user-header-image {
			width: 100%;
		}

		.user-info-box {
			background: #ffffff;
			padding: 30rpx;
			margin: -80rpx 20rpx 20rpx 20rpx;
			position: relative;
			border-radius: 20rpx;
			display: flex;
			align-items: center;

			.user-avg {
				width: 100rpx;
				height: 100rpx;
				border-radius: 10rpx;
			}

			.user-nickname {
				margin-left: 20rpx;
			}

			.user-phone {
				margin-left: 20rpx;
				font-size: 24rpx;
				color: #666666;
			}
		}

		.user-recharge-wrapper {
			background: #ffffff;
			border-radius: 20rpx;
			padding: 30rpx;
			margin: 20rpx;

			.user-recharge-box {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 20rpx;

				.recharge-info {
					.info-title {
						color: #333;
						font-size: 28rpx;
						font-weight: bold;
					}

					.info-content {
						color: #999;
						font-size: 24rpx;
					}
				}

				.recharge-button {
					// background: #ff4131;
					color: #333;
					padding: 10rpx 20rpx;
					font-size: 22rpx;
					border-radius: 50rpx;
					flex-shrink: 0;
					border: 1rpx solid #dadbde;
				}
			}

			.recharge-user-money {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-top: 20rpx;
				font-size: 26rpx;
				color: #333;

				.recharge-money {
					font-weight: bold;
					font-size: 30rpx;
				}

				.recharge-money::first-letter {
					font-size: 22rpx;
				}
			}
		}

		.user-activity-menu {
			padding: 30rpx;
			margin: 20rpx;
			border-radius: 20rpx;
			background: #fff;

			.menu-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-top: 60rpx;

				.left {
					display: flex;
					align-items: center;

					.menu-icon {
						width: 40rpx;
						height: 40rpx;
					}


					.menu-name {
						font-size: 28rpx;
						margin-left: 20rpx;
						color: #333;
					}
				}

				.menu-number {
					font-size: 30rpx;
					color: #666666;
					letter-spacing: 2rpx;
				}
			}

			.menu-item:first-child {
				margin-top: 0;
			}
		}
	}

	.bg {
		height: 400rpx;
		background: linear-gradient(to bottom, #c3cfe2, #fff);
	}

	.other-info-box {
		background: #ffffff;
		// padding: 30rpx;
		margin: -80rpx 20rpx 20rpx 20rpx;
		position: relative;
		border-radius: 20rpx;
		// display: flex;
		// align-items: center;
	}


	::v-deep .uni-section {
		background-color: transparent !important;
	}
</style>