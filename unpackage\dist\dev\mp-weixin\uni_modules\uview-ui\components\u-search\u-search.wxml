<view data-event-opts="{{[['tap',[['clickHandler',['$event']]]]]}}" class="u-search data-v-0a306a29" style="{{$root.s0}}" bindtap="__e"><view class="u-search__content data-v-0a306a29" style="{{'background-color:'+(bgColor)+';'+('border-radius:'+(shape=='round'?'100px':'4px')+';')+('border-color:'+(borderColor)+';')}}"><block wx:if="{{$slots.label||label!==null}}"><block wx:if="{{$slots.label}}"><slot name="label"></slot></block><block wx:else><text class="u-search__content__label data-v-0a306a29">{{label}}</text></block></block><view class="u-search__content__icon data-v-0a306a29"><u-icon vue-id="3bdd5bfd-1" size="{{searchIconSize}}" name="{{searchIcon}}" color="{{searchIconColor?searchIconColor:color}}" data-event-opts="{{[['^tap',[['clickIcon']]]]}}" bind:tap="__e" class="data-v-0a306a29" bind:__l="__l"></u-icon></view><input class="u-search__content__input data-v-0a306a29" style="{{$root.s1}}" confirm-type="search" disabled="{{disabled}}" focus="{{focus}}" maxlength="{{maxlength}}" placeholder-class="u-search__content__input--placeholder" placeholder="{{placeholder}}" placeholder-style="{{'color: '+placeholderColor}}" type="text" data-event-opts="{{[['blur',[['blur',['$event']]]],['confirm',[['search',['$event']]]],['input',[['inputChange',['$event']]]],['focus',[['getFocus',['$event']]]]]}}" value="{{value}}" bindblur="__e" bindconfirm="__e" bindinput="__e" bindfocus="__e"/><block wx:if="{{keyword&&clearabled&&focused}}"><view data-event-opts="{{[['tap',[['clear',['$event']]]]]}}" class="u-search__content__icon u-search__content__close data-v-0a306a29" bindtap="__e"><u-icon vue-id="3bdd5bfd-2" name="close" size="11" color="#ffffff" customStyle="line-height: 12px" class="data-v-0a306a29" bind:__l="__l"></u-icon></view></block></view><text data-event-opts="{{[['tap',[['custom',['$event']]]]]}}" class="{{['u-search__action','data-v-0a306a29',(showActionBtn||show)&&'u-search__action--active']}}" style="{{$root.s2}}" catchtap="__e">{{actionText}}</text></view>