{"version": 3, "sources": ["webpack:///D:/project/vt-unih5-order/uni_modules/uview-ui/components/u-avatar/u-avatar.vue?878e", "webpack:///D:/project/vt-unih5-order/uni_modules/uview-ui/components/u-avatar/u-avatar.vue?deb2", "webpack:///D:/project/vt-unih5-order/uni_modules/uview-ui/components/u-avatar/u-avatar.vue?98e8", "webpack:///D:/project/vt-unih5-order/uni_modules/uview-ui/components/u-avatar/u-avatar.vue?fec3", "uni-app:///uni_modules/uview-ui/components/u-avatar/u-avatar.vue", "webpack:///D:/project/vt-unih5-order/uni_modules/uview-ui/components/u-avatar/u-avatar.vue?b043", "webpack:///D:/project/vt-unih5-order/uni_modules/uview-ui/components/u-avatar/u-avatar.vue?b1cf"], "names": ["name", "mixins", "data", "colors", "avatarUrl", "allowMp", "watch", "src", "immediate", "handler", "computed", "imageStyle", "created", "methods", "init", "isImg", "<PERSON><PERSON><PERSON><PERSON>", "clickHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3EA;AAAA;AAAA;AAAA;AAAmoB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACwDvpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,mBACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAvBA,eAwBA;EACAA;EACAC;EACAC;IACA;MACA;MACAC,4GACA,mGACA,WACA,UACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACAC;MACAC;MACAC;QACA;QACA;QACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;;MAEA;IAEA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9IA;AAAA;AAAA;AAAA;AAA8tC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACAlvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-avatar/u-avatar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-avatar.vue?vue&type=template&id=d3651d6e&scoped=true&\"\nvar renderjs\nimport script from \"./u-avatar.vue?vue&type=script&lang=js&\"\nexport * from \"./u-avatar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-avatar.vue?vue&type=style&index=0&id=d3651d6e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d3651d6e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-avatar/u-avatar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-avatar.vue?vue&type=template&id=d3651d6e&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    \"u-Text\": function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u--text/u--text\" */ \"@/uni_modules/uview-ui/components/u--text/u--text.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([\n    {\n      backgroundColor:\n        _vm.text || _vm.icon\n          ? _vm.randomBgColor\n            ? _vm.colors[\n                _vm.colorIndex !== \"\" ? _vm.colorIndex : _vm.$u.random(0, 19)\n              ]\n            : _vm.bgColor\n          : \"transparent\",\n      width: _vm.$u.addUnit(_vm.size),\n      height: _vm.$u.addUnit(_vm.size),\n    },\n    _vm.$u.addStyle(_vm.customStyle),\n  ])\n  var g0 = _vm.mpAvatar && _vm.allowMp ? _vm.$u.addUnit(_vm.size) : null\n  var g1 = _vm.mpAvatar && _vm.allowMp ? _vm.$u.addUnit(_vm.size) : null\n  var g2 =\n    !(_vm.mpAvatar && _vm.allowMp) && !_vm.icon && !_vm.text\n      ? _vm.$u.addUnit(_vm.size)\n      : null\n  var g3 =\n    !(_vm.mpAvatar && _vm.allowMp) && !_vm.icon && !_vm.text\n      ? _vm.$u.addUnit(_vm.size)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-avatar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-avatar.vue?vue&type=script&lang=js&\"", "<template>\n\t<view\n\t\tclass=\"u-avatar\"\n\t\t:class=\"[`u-avatar--${shape}`]\"\n\t\t:style=\"[{\n\t\t\tbackgroundColor: (text || icon) ? (randomBgColor ? colors[colorIndex !== '' ? colorIndex : $u.random(0, 19)] : bgColor) : 'transparent',\n\t\t\twidth: $u.addUnit(size),\n\t\t\theight: $u.addUnit(size),\n\t\t}, $u.addStyle(customStyle)]\"\n\t\t@tap=\"clickHandler\"\n\t>\n\t\t<slot>\n\t\t\t<!-- #ifdef MP-WEIXIN || MP-QQ || MP-BAIDU  -->\n\t\t\t<open-data\n\t\t\t\tv-if=\"mpAvatar && allowMp\"\n\t\t\t\ttype=\"userAvatarUrl\"\n\t\t\t\t:style=\"[{\n\t\t\t\t\twidth: $u.addUnit(size),\n\t\t\t\t\theight: $u.addUnit(size)\n\t\t\t\t}]\"\n\t\t\t/>\n\t\t\t<!-- #endif -->\n\t\t\t<!-- #ifndef MP-WEIXIN && MP-QQ && MP-BAIDU  -->\n\t\t\t<template v-if=\"mpAvatar && allowMp\"></template>\n\t\t\t<!-- #endif -->\n\t\t\t<u-icon\n\t\t\t\tv-else-if=\"icon\"\n\t\t\t\t:name=\"icon\"\n\t\t\t\t:size=\"fontSize\"\n\t\t\t\t:color=\"color\"\n\t\t\t></u-icon>\n\t\t\t<u--text\n\t\t\t\tv-else-if=\"text\"\n\t\t\t\t:text=\"text\"\n\t\t\t\t:size=\"fontSize\"\n\t\t\t\t:color=\"color\"\n\t\t\t\talign=\"center\"\n\t\t\t\tcustomStyle=\"justify-content: center\"\n\t\t\t></u--text>\n\t\t\t<image\n\t\t\t\tclass=\"u-avatar__image\"\n\t\t\t\tv-else\n\t\t\t\t:class=\"[`u-avatar__image--${shape}`]\"\n\t\t\t\t:src=\"avatarUrl || defaultUrl\"\n\t\t\t\t:mode=\"mode\"\n\t\t\t\t@error=\"errorHandler\"\n\t\t\t\t:style=\"[{\n\t\t\t\t\twidth: $u.addUnit(size),\n\t\t\t\t\theight: $u.addUnit(size)\n\t\t\t\t}]\"\n\t\t\t></image>\n\t\t</slot>\n\t</view>\n</template>\n\n<script>\n\timport props from './props.js';\n\tconst base64Avatar =\n\t\t\"data:image/jpg;base64,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\";\n\t/**\n\t * Avatar  头像\n\t * @description 本组件一般用于展示头像的地方，如个人中心，或者评论列表页的用户头像展示等场所。\n\t * @tutorial https://www.uviewui.com/components/avatar.html\n\t *\n\t * @property {String}\t\t\tsrc\t\t\t\t头像路径，如加载失败，将会显示默认头像(不能为相对路径)\n\t * @property {String}\t\t\tshape\t\t\t头像形状  （ circle (默认) | square）\n\t * @property {String | Number}\tsize\t\t\t头像尺寸，可以为指定字符串(large, default, mini)，或者数值 （默认 40 ）\n\t * @property {String}\t\t\tmode\t\t\t头像图片的裁剪类型，与uni的image组件的mode参数一致，如效果达不到需求，可尝试传widthFix值 （默认 'scaleToFill' ）\n\t * @property {String}\t\t\ttext\t\t\t用文字替代图片，级别优先于src\n\t * @property {String}\t\t\tbgColor\t\t\t背景颜色，一般显示文字时用 （默认 '#c0c4cc' ）\n\t * @property {String}\t\t\tcolor\t\t\t文字颜色 （默认 '#ffffff' ）\n\t * @property {String | Number}\tfontSize\t\t文字大小  （默认 18 ）\n\t * @property {String}\t\t\ticon\t\t\t显示的图标\n\t * @property {Boolean}\t\t\tmpAvatar\t\t显示小程序头像，只对百度，微信，QQ小程序有效  （默认 false ）\n\t * @property {Boolean}\t\t\trandomBgColor\t是否使用随机背景色  （默认 false ）\n\t * @property {String}\t\t\tdefaultUrl\t\t加载失败的默认头像(组件有内置默认图片)\n\t * @property {String | Number}\tcolorIndex\t\t如果配置了randomBgColor为true，且配置了此值，则从默认的背景色数组中取出对应索引的颜色值，取值0-19之间\n\t * @property {String}\t\t\tname\t\t\t组件标识符  （默认 'level' ）\n\t * @property {Object}\t\t\tcustomStyle\t\t定义需要用到的外部样式\n\t *\n\t * @event    {Function}        click       点击组件时触发   index: 用户传递的标识符\n\t * @example  <u-avatar :src=\"src\" mode=\"square\"></u-avatar>\n\t */\n\texport default {\n\t\tname: 'u-avatar',\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 如果配置randomBgColor参数为true，在图标或者文字的模式下，会随机从中取出一个颜色值当做背景色\n\t\t\t\tcolors: ['#ffb34b', '#f2bba9', '#f7a196', '#f18080', '#88a867', '#bfbf39', '#89c152', '#94d554', '#f19ec2',\n\t\t\t\t\t'#afaae4', '#e1b0df', '#c38cc1', '#72dcdc', '#9acdcb', '#77b1cc', '#448aca', '#86cefa', '#98d1ee',\n\t\t\t\t\t'#73d1f1',\n\t\t\t\t\t'#80a7dc'\n\t\t\t\t],\n\t\t\t\tavatarUrl: this.src,\n\t\t\t\tallowMp: false\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\t// 监听头像src的变化，赋值给内部的avatarUrl变量，因为图片加载失败时，需要修改图片的src为默认值\n\t\t\t// 而组件内部不能直接修改props的值，所以需要一个中间变量\n\t\t\tsrc: {\n\t\t\t\timmediate: true,\n\t\t\t\thandler(newVal) {\n\t\t\t\t\tthis.avatarUrl = newVal\n\t\t\t\t\t// 如果没有传src，则主动触发error事件，用于显示默认的头像，否则src为''空字符等的时候，会无内容展示\n\t\t\t\t\tif(!newVal) {\n\t\t\t\t\t\tthis.errorHandler()\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\timageStyle() {\n\t\t\t\tconst style = {}\n\t\t\t\treturn style\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.init()\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\t// 目前只有这几个小程序平台具有open-data标签\n\t\t\t\t// 其他平台可以通过uni.getUserInfo类似接口获取信息，但是需要弹窗授权(首次)，不合符组件逻辑\n\t\t\t\t// 故目前自动获取小程序头像只支持这几个平台\n\t\t\t\t// #ifdef MP-WEIXIN || MP-QQ || MP-BAIDU\n\t\t\t\tthis.allowMp = true\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 判断传入的name属性，是否图片路径，只要带有\"/\"均认为是图片形式\n\t\t\tisImg() {\n\t\t\t\treturn this.src.indexOf('/') !== -1\n\t\t\t},\n\t\t\t// 图片加载时失败时触发\n\t\t\terrorHandler() {\n\t\t\t\tthis.avatarUrl = this.defaultUrl || base64Avatar\n\t\t\t},\n\t\t\tclickHandler() {\n\t\t\t\tthis.$emit('click', this.name)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-avatar {\n\t\t@include flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\n\t\t&--circle {\n\t\t\tborder-radius: 100px;\n\t\t}\n\n\t\t&--square {\n\t\t\tborder-radius: 4px;\n\t\t}\n\n\t\t&__image {\n\t\t\t&--circle {\n\t\t\t\tborder-radius: 100px;\n\t\t\t\toverflow: hidden;\n\t\t\t}\n\n\t\t\t&--square {\n\t\t\t\tborder-radius: 4px;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-avatar.vue?vue&type=style&index=0&id=d3651d6e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-avatar.vue?vue&type=style&index=0&id=d3651d6e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759027613735\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}