{"version": 3, "sources": ["webpack:///D:/project/vt-unih5-order/uni_modules/uv-upload/components/uv-upload/uv-upload.vue?5d5a", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-upload/components/uv-upload/uv-upload.vue?1964", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-upload/components/uv-upload/uv-upload.vue?4d54", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-upload/components/uv-upload/uv-upload.vue?1528", "uni-app:///uni_modules/uv-upload/components/uv-upload/uv-upload.vue", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-upload/components/uv-upload/uv-upload.vue?3bad", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-upload/components/uv-upload/uv-upload.vue?429c"], "names": ["name", "mixins", "data", "lists", "isInCount", "watch", "fileList", "deep", "immediate", "handler", "deletable", "item", "methods", "formatFileList", "maxCount", "Object", "isImage", "isVideo", "chooseFile", "multiple", "disabled", "capture", "accept", "compressed", "maxDuration", "sizeType", "camera", "then", "catch", "onBeforeRead", "beforeRead", "useBeforeRead", "res", "file", "callback", "ok", "getDetail", "index", "onAfterRead", "maxSize", "afterRead", "deleteItem", "onPreviewImage", "i", "uni", "urls", "map", "current", "fail", "onPreviewVideo", "onClickPreview"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,4WAEN;AACP,KAAK;AACL;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnFA;AAAA;AAAA;AAAA;AAAooB,CAAgB,mnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACmExpB;AAOA;AACA;AACA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjCA,eAkCA;EACAA;EAIAC;EACAC;IACA;MAIAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACAC;QACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA,qBAEA,KADAP;QAAAA;QAAAQ,WACA,KADAA;MAEA;QAAA,OACAC;UACA;UACAC;UACAC;UACAP;QACA;MAAA,EACA;MACA;MACA;IACA;IACAQ;MAAA;MACA;MACA;QACA,IACAJ,WAIA,OAJAA;UACAK,WAGA,OAHAA;UACAhB,QAEA,OAFAA;UACAiB,WACA,OADAA;QAEA;QACA;QACA;QACA;UACAC;QACA;UACAA;QACA;QACA,uBACAN;UACAO;UACAH;UACAE;UACAE;UACAC;UACAC;UACAC;QACA;UACAZ;QACA,GACA,CACAa;UACA;QACA,GACAC;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA,IACAC,aAEA,KAFAA;QACAC,gBACA,KADAA;MAEA;MACA;MACA;QACA;QACAC;MACA;MACA;QACAA;UACA,aACA,cACAjB;YACAkB;UACA;YACAC;cACAC;YACA;UACA,GACA;QACA;MACA;MACA;QACA;MACA;MACA;QACAH;UAAA;QAAA;MACA;QACA;MACA;IACA;IACAI;MACA;QACApC;QACAqC;MACA;IACA;IACAC;MACA,IACAC,UAEA,KAFAA;QACAC,YACA,KADAA;MAEA,qCACAP;QAAA;MAAA,KACAA;MACA;QACA;UACAA;QACA;QACA;MACA;MACA;QACAO;MACA;MACA;QACAP;MACA;IACA;IACAQ;MACA,WACA,UACA1B;QACAkB;MACA,GACA;IACA;IACA;IACAS;MAAA;MACA;MACAvC;QACA;UACAwC;QACA;MACA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;MACA;MACAC;QACA;QACAC;UAAA;QAAA,GACAC;UAAA;QAAA;QACAC;QACAC;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA,WACA,gBACAnC,8DACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjTA;AAAA;AAAA;AAAA;AAA+tC,CAAgB,0nCAAG,EAAC,C;;;;;;;;;;;ACAnvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uv-upload/components/uv-upload/uv-upload.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uv-upload.vue?vue&type=template&id=2e4c50f9&scoped=true&\"\nvar renderjs\nimport script from \"./uv-upload.vue?vue&type=script&lang=js&\"\nexport * from \"./uv-upload.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uv-upload.vue?vue&type=style&index=0&id=2e4c50f9&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2e4c50f9\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uv-upload/components/uv-upload/uv-upload.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-upload.vue?vue&type=template&id=2e4c50f9&scoped=true&\"", "var components\ntry {\n  components = {\n    uvIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-icon/components/uv-icon/uv-icon\" */ \"@/uni_modules/uv-icon/components/uv-icon/uv-icon.vue\"\n      )\n    },\n    uvLoadingIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-loading-icon/components/uv-loading-icon/uv-loading-icon\" */ \"@/uni_modules/uv-loading-icon/components/uv-loading-icon/uv-loading-icon.vue\"\n      )\n    },\n    uvPreviewVideo: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-upload/components/uv-preview-video/uv-preview-video\" */ \"@/uni_modules/uv-upload/components/uv-preview-video/uv-preview-video.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.$uv.addStyle(_vm.customStyle)])\n  var l0 = _vm.previewImage\n    ? _vm.__map(_vm.lists, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 =\n          item.isImage || (item.type && item.type === \"image\")\n            ? _vm.$uv.addUnit(_vm.width)\n            : null\n        var g1 =\n          item.isImage || (item.type && item.type === \"image\")\n            ? _vm.$uv.addUnit(_vm.height)\n            : null\n        var g2 = !(item.isImage || (item.type && item.type === \"image\"))\n          ? _vm.$uv.addUnit(_vm.width)\n          : null\n        var g3 = !(item.isImage || (item.type && item.type === \"image\"))\n          ? _vm.$uv.addUnit(_vm.height)\n          : null\n        return {\n          $orig: $orig,\n          g0: g0,\n          g1: g1,\n          g2: g2,\n          g3: g3,\n        }\n      })\n    : null\n  var g4 = _vm.isInCount ? _vm.$uv.addUnit(_vm.width) : null\n  var g5 = _vm.isInCount ? _vm.$uv.addUnit(_vm.height) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        l0: l0,\n        g4: g4,\n        g5: g5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-upload.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-upload.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uv-upload\" :style=\"[$uv.addStyle(customStyle)]\">\r\n\t\t<view class=\"uv-upload__wrap\">\r\n\t\t\t<template v-if=\"previewImage\">\r\n\t\t\t\t<view class=\"uv-upload__wrap__preview\" v-for=\"(item, index) in lists\" :key=\"index\">\r\n\t\t\t\t\t<image v-if=\"item.isImage || (item.type && item.type === 'image')\" :src=\"item.thumb || item.url\"\r\n\t\t\t\t\t\t:mode=\"imageMode\" class=\"uv-upload__wrap__preview__image\" @tap=\"onPreviewImage(item,index)\"\r\n\t\t\t\t\t\t:style=\"[{\r\n\t\t\t\t\t\t\twidth: $uv.addUnit(width),\r\n\t\t\t\t\t\t\theight: $uv.addUnit(height)\r\n\t\t\t\t\t\t}]\" />\r\n\t\t\t\t\t<view v-else class=\"uv-upload__wrap__preview__other\" @tap=\"onPreviewVideo(item,index)\" :style=\"[{\r\n\t\t\t\t\t\t\twidth: $uv.addUnit(width),\r\n\t\t\t\t\t\t\theight: $uv.addUnit(height)\r\n\t\t\t\t\t\t}]\">\r\n\t\t\t\t\t\t<uv-icon color=\"#80CBF9\" size=\"26\"\r\n\t\t\t\t\t\t\t:name=\"item.isVideo || (item.type && item.type === 'video') ? 'movie' : 'folder'\"></uv-icon>\r\n\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\tclass=\"uv-upload__wrap__preview__other__text\">{{item.isVideo || (item.type && item.type === 'video') ? '视频' : '文件'}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uv-upload__status\" v-if=\"item.status === 'uploading' || item.status === 'failed'\">\r\n\t\t\t\t\t\t<view class=\"uv-upload__status__icon\">\r\n\t\t\t\t\t\t\t<uv-icon v-if=\"item.status === 'failed'\" name=\"close-circle\" color=\"#ffffff\" size=\"25\" />\r\n\t\t\t\t\t\t\t<uv-loading-icon size=\"22\" mode=\"circle\" v-else />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text v-if=\"item.message\" class=\"uv-upload__status__message\">{{ item.message }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uv-upload__deletable\"\r\n\t\t\t\t\t\tv-if=\"item.status !== 'uploading' && (deletable || item.deletable)\"\r\n\t\t\t\t\t\**********=\"deleteItem(index)\">\r\n\t\t\t\t\t\t<view class=\"uv-upload__deletable__icon\">\r\n\t\t\t\t\t\t\t<uv-icon name=\"close\" color=\"#ffffff\" size=\"10\"></uv-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uv-upload__success\" v-if=\"item.status === 'success'\">\r\n\t\t\t\t\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t\t\t\t\t<image :src=\"successIcon\" class=\"uv-upload__success__icon\"></image>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t\t\t\t\t<view class=\"uv-upload__success__icon\">\r\n\t\t\t\t\t\t\t<uv-icon name=\"checkmark\" color=\"#ffffff\" size=\"12\"></uv-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</template>\r\n\t\t\t<template v-if=\"isInCount\">\r\n\t\t\t\t<view @tap=\"chooseFile\">\r\n\t\t\t\t\t<slot>\r\n\t\t\t\t\t\t<view class=\"uv-upload__button\" :hover-class=\"!disabled ? 'uv-upload__button--hover' : ''\"\r\n\t\t\t\t\t\t\thover-stay-time=\"150\" @tap.stop=\"chooseFile\"\r\n\t\t\t\t\t\t\t:class=\"[disabled && 'uv-upload__button--disabled']\" :style=\"[{\r\n\t\t\t\t\t\t\t\twidth: $uv.addUnit(width),\r\n\t\t\t\t\t\t\t\theight: $uv.addUnit(height)\r\n\t\t\t\t\t\t\t}]\">\r\n\t\t\t\t\t\t\t<uv-icon :name=\"uploadIcon\" size=\"26\" :color=\"uploadIconColor\"></uv-icon>\r\n\t\t\t\t\t\t\t<text v-if=\"uploadText\" class=\"uv-upload__button__text\">{{ uploadText }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t</view>\r\n\t\t\t</template>\r\n\t\t</view>\r\n\t\t<uv-preview-video ref=\"previewVideo\"></uv-preview-video>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tfunc,\r\n\t\timage,\r\n\t\tvideo,\r\n\t\tarray,\r\n\t\tpromise\r\n\t} from '@/uni_modules/uv-ui-tools/libs/function/test.js';\r\n\timport mpMixin from '@/uni_modules/uv-ui-tools/libs/mixin/mpMixin.js'\r\n\timport mixin from '@/uni_modules/uv-ui-tools/libs/mixin/mixin.js'\r\n\timport {\r\n\t\tchooseFile\r\n\t} from './utils';\r\n\timport mixin_accept from './mixin.js';\r\n\timport props from './props.js';\r\n\t/**\r\n\t * upload 上传\r\n\t * @description 该组件用于上传图片场景\r\n\t * @tutorial https://www.uvui.cn/components/upload.html\r\n\t * @property {String}\t\t\taccept\t\t\t\t接受的文件类型, 可选值为all media image file video （默认 'image' ）\r\n\t * @property {String | Array}\tcapture\t\t\t\t图片或视频拾取模式，当accept为image类型时设置capture可选额外camera可以直接调起摄像头（默认 ['album', 'camera'] ）\r\n\t * @property {Boolean}\t\t\tcompressed\t\t\t当accept为video时生效，是否压缩视频，默认为true（默认 true ）\r\n\t * @property {String}\t\t\tcamera\t\t\t\t当accept为video时生效，可选值为back或front（默认 'back' ）\r\n\t * @property {Number}\t\t\tmaxDuration\t\t\t当accept为video时生效，拍摄视频最长拍摄时间，单位秒（默认 60 ）\r\n\t * @property {String}\t\t\tuploadIcon\t\t\t上传区域的图标，只能内置图标（默认 'camera-fill' ）\r\n\t * @property {String}\t\t\tuploadIconColor\t\t上传区域的图标的字体颜色，只能内置图标（默认 #D3D4D6 ）\r\n\t * @property {Boolean}\t\t\tuseBeforeRead\t\t是否开启文件读取前事件（默认 false ）\r\n\t * @property {Boolean}\t\t\tpreviewFullImage\t是否开启图片预览功能（默认 true ）\r\n\t * @property {Boolean}\t\t\tpreviewFullVideo\t是否开启视频预览功能（默认 true ）\r\n\t * @property {String | Number}\tmaxCount\t\t\t最大上传数量（默认 52 ）\r\n\t * @property {Boolean}\t\t\tdisabled\t\t\t是否启用（默认 false ）\r\n\t * @property {String}\t\t\timageMode\t\t\t预览上传的图片时的裁剪模式，和image组件mode属性一致（默认 'aspectFill' ）\r\n\t * @property {String}\t\t\tname\t\t\t\t标识符，可以在回调函数的第二项参数中获取\r\n\t * @property {Array}\t\t\tsizeType\t\t\t所选的图片的尺寸, 可选值为original compressed（默认 ['original', 'compressed'] ）\r\n\t * @property {Boolean}\t\t\tmultiple\t\t\t是否开启图片多选，部分安卓机型不支持 （默认 false ）\r\n\t * @property {Boolean}\t\t\tdeletable\t\t\t是否展示删除按钮（默认 true ）\r\n\t * @property {String | Number}\tmaxSize\t\t\t\t文件大小限制，单位为byte （默认 Number.MAX_VALUE ）\r\n\t * @property {Array}\t\t\tfileList\t\t\t显示已上传的文件列表\r\n\t * @property {String}\t\t\tuploadText\t\t\t上传区域的提示文字\r\n\t * @property {String | Number}\twidth\t\t\t\t内部预览图片区域和选择图片按钮的区域宽度（默认 80 ）\r\n\t * @property {String | Number}\theight\t\t\t\t内部预览图片区域和选择图片按钮的区域高度（默认 80 ）\r\n\t * @property {Object}\t\t\tcustomStyle\t\t\t组件的样式，对象形式\r\n\t * @event {Function} afterRead\t\t读取后的处理函数\r\n\t * @event {Function} beforeRead\t\t读取前的处理函数\r\n\t * @event {Function} oversize\t\t文件超出大小限制\r\n\t * @event {Function} clickPreview\t点击预览时触发\r\n\t * @event {Function} delete \t\t删除图片\r\n\t * @example <uv-upload :action=\"action\" :fileList=\"fileList\" ></uv-upload>\r\n\t */\r\n\texport default {\r\n\t\tname: \"uv-upload\",\r\n\t\t// #ifdef VUE3\r\n\t\temits: ['error', 'beforeRead', 'oversize', 'afterRead', 'delete', 'clickPreview'],\r\n\t\t// #endif\r\n\t\tmixins: [mpMixin, mixin, mixin_accept, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tsuccessIcon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAKKADAAQAAAABAAAAKAAAAAB65masAAACP0lEQVRYCc3YXygsURwH8K/dpcWyG3LF5u/6/+dKVylSypuUl6uUPMifKMWL8oKEB1EUT1KeUPdR3uTNUsSLxb2udG/cbvInNuvf2rVnazZ/ZndmZ87snjM1Z+Z3zpzfp9+Z5mEAhlvjRtZgCKs+gnPAOcAkkMOR4jEHfItjDvgRxxSQD8cM0BuOCaAvXNCBQrigAsXgggYUiwsK0B9cwIH+4gIKlIILGFAqLiBAOTjFgXJxigJp4BQD0sIpAqSJow6kjSNAFTnRaHJwLenD6Mud52VQAcrBfTd2oyq+HtGaGGWAcnAVcXWoM3bCZrdi+ncPfaAcXE5UKVpdW/vitGPqqAtn98d0gXJwX7Qp6MmegUYVhvmTIezdmHlxJCjpHRTCFerLkRRu4k0aqdajN3sWOo0BK//msHa+xDuPC/oNFMKRhTtM4xjIX0SCNpXL4+7VIaHuyiWEp2L7ahWLf8fejfPdqPmC3mJicORZUp1CQzm+GiphvljGk+PBvWRbxii+xVTj5M6CiZ/tsDufvaXyxEUDxeLIyvu3m0iOyEFWVAkydcVYdyFrE9tQk9iMq6f/GNlvwt3LjQfh60LUrw9/cFyyMJUW/XkLSNMV4Mi6C5ML+ui4x5ClAX9sB9w0wV6wglJwJCv5fOxcr6EstgbGiEw4XcfUry4cWrcEUW8n+ARKxXEJHhw2WG43UKSvwI/TSZgvl7kh0b3XLZaLEy0QmMgLZAVH7J+ALOE+AVnDvQOyiPMAWcW5gSzjCPAV+78S5WE0GrQAAAAASUVORK5CYII=',\r\n\t\t\t\t// #endif\r\n\t\t\t\tlists: [],\r\n\t\t\t\tisInCount: true,\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// 监听文件列表的变化，重新整理内部数据\r\n\t\t\tfileList: {\r\n\t\t\t\tdeep: true,\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler() {\r\n\t\t\t\t\tthis.formatFileList()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tdeletable(newVal) {\r\n\t\t\t\tif (!newVal) {\r\n\t\t\t\t\tthis.lists.map(item => {\r\n\t\t\t\t\t\titem.deletable = this.deletable;\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tformatFileList() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tfileList = [], maxCount\r\n\t\t\t\t} = this;\r\n\t\t\t\tconst lists = fileList.map((item) =>\r\n\t\t\t\t\tObject.assign(Object.assign({}, item), {\r\n\t\t\t\t\t\t// 如果item.url为本地选择的blob文件的话，无法判断其为video还是image，此处优先通过accept做判断处理\r\n\t\t\t\t\t\tisImage: this.accept === 'image' || image(item.url || item.thumb),\r\n\t\t\t\t\t\tisVideo: this.accept === 'video' || video(item.url || item.thumb),\r\n\t\t\t\t\t\tdeletable: typeof(item.deletable) === 'boolean' ? item.deletable : this.deletable,\r\n\t\t\t\t\t})\r\n\t\t\t\t);\r\n\t\t\t\tthis.lists = lists\r\n\t\t\t\tthis.isInCount = lists.length < maxCount\r\n\t\t\t},\r\n\t\t\tchooseFile() {\r\n\t\t\t\tthis.timer && clearTimeout(this.timer);\r\n\t\t\t\tthis.timer = setTimeout(() => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tmaxCount,\r\n\t\t\t\t\t\tmultiple,\r\n\t\t\t\t\t\tlists,\r\n\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t} = this;\r\n\t\t\t\t\tif (disabled) return;\r\n\t\t\t\t\t// 如果用户传入的是字符串，需要格式化成数组\r\n\t\t\t\t\tlet capture;\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tcapture = array(this.capture) ? this.capture : this.capture.split(',');\r\n\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\tcapture = [];\r\n\t\t\t\t\t}\r\n\t\t\t\t\tchooseFile(\r\n\t\t\t\t\t\t\tObject.assign({\r\n\t\t\t\t\t\t\t\taccept: this.accept,\r\n\t\t\t\t\t\t\t\tmultiple: this.multiple,\r\n\t\t\t\t\t\t\t\tcapture: capture,\r\n\t\t\t\t\t\t\t\tcompressed: this.compressed,\r\n\t\t\t\t\t\t\t\tmaxDuration: this.maxDuration,\r\n\t\t\t\t\t\t\t\tsizeType: this.sizeType,\r\n\t\t\t\t\t\t\t\tcamera: this.camera,\r\n\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\tmaxCount: maxCount - lists.length,\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t)\r\n\t\t\t\t\t\t.then((res) => {\r\n\t\t\t\t\t\t\tthis.onBeforeRead(multiple ? res : res[0]);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch((error) => {\r\n\t\t\t\t\t\t\tthis.$emit('error', error);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t}, 100)\r\n\t\t\t},\r\n\t\t\t// 文件读取之前\r\n\t\t\tonBeforeRead(file) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tbeforeRead,\r\n\t\t\t\t\tuseBeforeRead,\r\n\t\t\t\t} = this;\r\n\t\t\t\tlet res = true\r\n\t\t\t\t// beforeRead是否为一个方法\r\n\t\t\t\tif (func(beforeRead)) {\r\n\t\t\t\t\t// 如果用户定义了此方法，则去执行此方法，并传入读取的文件回调\r\n\t\t\t\t\tres = beforeRead(file, this.getDetail());\r\n\t\t\t\t}\r\n\t\t\t\tif (useBeforeRead) {\r\n\t\t\t\t\tres = new Promise((resolve, reject) => {\r\n\t\t\t\t\t\tthis.$emit(\r\n\t\t\t\t\t\t\t'beforeRead',\r\n\t\t\t\t\t\t\tObject.assign(Object.assign({\r\n\t\t\t\t\t\t\t\tfile\r\n\t\t\t\t\t\t\t}, this.getDetail()), {\r\n\t\t\t\t\t\t\t\tcallback: (ok) => {\r\n\t\t\t\t\t\t\t\t\tok ? resolve() : reject();\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tif (!res) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (promise(res)) {\r\n\t\t\t\t\tres.then((data) => this.onAfterRead(data || file));\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.onAfterRead(file);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetDetail(index) {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tname: this.name,\r\n\t\t\t\t\tindex: index == null ? this.fileList.length : index,\r\n\t\t\t\t};\r\n\t\t\t},\r\n\t\t\tonAfterRead(file) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tmaxSize,\r\n\t\t\t\t\tafterRead\r\n\t\t\t\t} = this;\r\n\t\t\t\tconst oversize = Array.isArray(file) ?\r\n\t\t\t\t\tfile.some((item) => item.size > maxSize) :\r\n\t\t\t\t\tfile.size > maxSize;\r\n\t\t\t\tif (oversize) {\r\n\t\t\t\t\tthis.$emit('oversize', Object.assign({\r\n\t\t\t\t\t\tfile\r\n\t\t\t\t\t}, this.getDetail()));\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (typeof afterRead === 'function') {\r\n\t\t\t\t\tafterRead(file, this.getDetail());\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('afterRead', Object.assign({\r\n\t\t\t\t\tfile\r\n\t\t\t\t}, this.getDetail()));\r\n\t\t\t},\r\n\t\t\tdeleteItem(index) {\r\n\t\t\t\tthis.$emit(\r\n\t\t\t\t\t'delete',\r\n\t\t\t\t\tObject.assign(Object.assign({}, this.getDetail(index)), {\r\n\t\t\t\t\t\tfile: this.fileList[index],\r\n\t\t\t\t\t})\r\n\t\t\t\t);\r\n\t\t\t},\r\n\t\t\t// 预览图片\r\n\t\t\tonPreviewImage(item, index) {\r\n\t\t\t\tconst lists = this.$uv.deepClone(this.lists);\r\n\t\t\t\tlists.map((i, j) => {\r\n\t\t\t\t\tif (j == index) {\r\n\t\t\t\t\t\ti.current = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\tconst filters = lists.filter(i => i.isImage);\r\n\t\t\t\tconst findIndex = filters.findIndex(i => i.current);\r\n\t\t\t\tthis.onClickPreview(item, index);\r\n\t\t\t\tif (!item.isImage || !this.previewFullImage) return\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t// 先filter找出为图片的item，再返回filter结果中的图片url\r\n\t\t\t\t\turls: this.lists.filter((item) => this.accept === 'image' || image(item.url || item.thumb))\r\n\t\t\t\t\t\t.map((item) => item.url || item.thumb),\r\n\t\t\t\t\tcurrent: findIndex,\r\n\t\t\t\t\tfail() {\r\n\t\t\t\t\t\tthis.$uv.toast('预览图片失败')\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tonPreviewVideo(item, index) {\r\n\t\t\t\tthis.onClickPreview(item, index);\r\n\t\t\t\tif (!this.previewFullVideo || !item.isVideo) return;\r\n\t\t\t\tthis.$refs.previewVideo.open(item.url);\r\n\t\t\t},\r\n\t\t\tonClickPreview(item, index) {\r\n\t\t\t\tthis.$emit(\r\n\t\t\t\t\t'clickPreview',\r\n\t\t\t\t\tObject.assign(Object.assign({}, item), this.getDetail(index))\r\n\t\t\t\t);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import '@/uni_modules/uv-ui-tools/libs/css/components.scss';\r\n\t@import '@/uni_modules/uv-ui-tools/libs/css/color.scss';\r\n\t$uv-upload-preview-border-radius: 2px !default;\r\n\t$uv-upload-preview-margin: 0 8px 8px 0 !default;\r\n\t$uv-upload-image-width: 80px !default;\r\n\t$uv-upload-image-height: $uv-upload-image-width;\r\n\t$uv-upload-other-bgColor: rgb(242, 242, 242) !default;\r\n\t$uv-upload-other-flex: 1 !default;\r\n\t$uv-upload-text-font-size: 11px !default;\r\n\t$uv-upload-text-color: $uv-tips-color !default;\r\n\t$uv-upload-text-margin-top: 2px !default;\r\n\t$uv-upload-deletable-right: 0 !default;\r\n\t$uv-upload-deletable-top: 0 !default;\r\n\t$uv-upload-deletable-bgColor: rgb(55, 55, 55) !default;\r\n\t$uv-upload-deletable-height: 14px !default;\r\n\t$uv-upload-deletable-width: $uv-upload-deletable-height;\r\n\t$uv-upload-deletable-boder-bottom-left-radius: 100px !default;\r\n\t$uv-upload-deletable-zIndex: 3 !default;\r\n\t$uv-upload-success-bottom: 0 !default;\r\n\t$uv-upload-success-right: 0 !default;\r\n\t$uv-upload-success-border-style: solid !default;\r\n\t$uv-upload-success-border-top-color: transparent !default;\r\n\t$uv-upload-success-border-left-color: transparent !default;\r\n\t$uv-upload-success-border-bottom-color: $uv-success !default;\r\n\t$uv-upload-success-border-right-color: $uv-upload-success-border-bottom-color;\r\n\t$uv-upload-success-border-width: 9px !default;\r\n\t$uv-upload-icon-top: 0px !default;\r\n\t$uv-upload-icon-right: 0px !default;\r\n\t$uv-upload-icon-h5-top: 1px !default;\r\n\t$uv-upload-icon-h5-right: 0 !default;\r\n\t$uv-upload-icon-width: 16px !default;\r\n\t$uv-upload-icon-height: $uv-upload-icon-width;\r\n\t$uv-upload-success-icon-bottom: -10px !default;\r\n\t$uv-upload-success-icon-right: -10px !default;\r\n\t$uv-upload-status-right: 0 !default;\r\n\t$uv-upload-status-left: 0 !default;\r\n\t$uv-upload-status-bottom: 0 !default;\r\n\t$uv-upload-status-top: 0 !default;\r\n\t$uv-upload-status-bgColor: rgba(0, 0, 0, 0.5) !default;\r\n\t$uv-upload-status-icon-Zindex: 1 !default;\r\n\t$uv-upload-message-font-size: 12px !default;\r\n\t$uv-upload-message-color: #FFFFFF !default;\r\n\t$uv-upload-message-margin-top: 5px !default;\r\n\t$uv-upload-button-width: 80px !default;\r\n\t$uv-upload-button-height: $uv-upload-button-width;\r\n\t$uv-upload-button-bgColor: rgb(244, 245, 247) !default;\r\n\t$uv-upload-button-border-radius: 2px !default;\r\n\t$uv-upload-botton-margin: 0 8px 8px 0 !default;\r\n\t$uv-upload-text-font-size: 11px !default;\r\n\t$uv-upload-text-color: $uv-tips-color !default;\r\n\t$uv-upload-text-margin-top: 2px !default;\r\n\t$uv-upload-hover-bgColor: rgb(230, 231, 233) !default;\r\n\t$uv-upload-disabled-opacity: .5 !default;\r\n\r\n\t.uv-upload {\r\n\t\t@include flex(column);\r\n\t\tflex: 1;\r\n\r\n\t\t&__wrap {\r\n\t\t\t@include flex;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\tflex: 1;\r\n\r\n\t\t\t&__preview {\r\n\t\t\t\tborder-radius: $uv-upload-preview-border-radius;\r\n\t\t\t\tmargin: $uv-upload-preview-margin;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\t@include flex;\r\n\r\n\t\t\t\t&__image {\r\n\t\t\t\t\twidth: $uv-upload-image-width;\r\n\t\t\t\t\theight: $uv-upload-image-height;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&__other {\r\n\t\t\t\t\twidth: $uv-upload-image-width;\r\n\t\t\t\t\theight: $uv-upload-image-height;\r\n\t\t\t\t\tbackground-color: $uv-upload-other-bgColor;\r\n\t\t\t\t\tflex: $uv-upload-other-flex;\r\n\t\t\t\t\t@include flex(column);\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t&__text {\r\n\t\t\t\t\t\tfont-size: $uv-upload-text-font-size;\r\n\t\t\t\t\t\tcolor: $uv-upload-text-color;\r\n\t\t\t\t\t\tmargin-top: $uv-upload-text-margin-top;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__deletable {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: $uv-upload-deletable-top;\r\n\t\t\tright: $uv-upload-deletable-right;\r\n\t\t\tbackground-color: $uv-upload-deletable-bgColor;\r\n\t\t\theight: $uv-upload-deletable-height;\r\n\t\t\twidth: $uv-upload-deletable-width;\r\n\t\t\t@include flex;\r\n\t\t\tborder-bottom-left-radius: $uv-upload-deletable-boder-bottom-left-radius;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tz-index: $uv-upload-deletable-zIndex;\r\n\r\n\t\t\t&__icon {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttransform: scale(0.7);\r\n\t\t\t\ttop: $uv-upload-icon-top;\r\n\t\t\t\tright: $uv-upload-icon-right;\r\n\t\t\t\t/* #ifdef H5 */\r\n\t\t\t\ttop: $uv-upload-icon-h5-top;\r\n\t\t\t\tright: $uv-upload-icon-h5-right;\r\n\t\t\t\t/* #endif */\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__success {\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: $uv-upload-success-bottom;\r\n\t\t\tright: $uv-upload-success-right;\r\n\t\t\t@include flex;\r\n\t\t\t// 由于weex(nvue)为阿里巴巴的KPI(部门业绩考核)的laji产物，不支持css绘制三角形\r\n\t\t\t// 所以在nvue下使用图片，非nvue下使用css实现\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tborder-style: $uv-upload-success-border-style;\r\n\t\t\tborder-top-color: $uv-upload-success-border-top-color;\r\n\t\t\tborder-left-color: $uv-upload-success-border-left-color;\r\n\t\t\tborder-bottom-color: $uv-upload-success-border-bottom-color;\r\n\t\t\tborder-right-color: $uv-upload-success-border-right-color;\r\n\t\t\tborder-width: $uv-upload-success-border-width;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\r\n\t\t\t/* #endif */\r\n\t\t\t&__icon {\r\n\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttransform: scale(0.7);\r\n\t\t\t\tbottom: $uv-upload-success-icon-bottom;\r\n\t\t\t\tright: $uv-upload-success-icon-right;\r\n\t\t\t\t/* #endif */\r\n\t\t\t\t/* #ifdef APP-NVUE */\r\n\t\t\t\twidth: $uv-upload-icon-width;\r\n\t\t\t\theight: $uv-upload-icon-height;\r\n\t\t\t\t/* #endif */\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__status {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: $uv-upload-status-top;\r\n\t\t\tbottom: $uv-upload-status-bottom;\r\n\t\t\tleft: $uv-upload-status-left;\r\n\t\t\tright: $uv-upload-status-right;\r\n\t\t\tbackground-color: $uv-upload-status-bgColor;\r\n\t\t\t@include flex(column);\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\r\n\t\t\t&__icon {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tz-index: $uv-upload-status-icon-Zindex;\r\n\t\t\t}\r\n\r\n\t\t\t&__message {\r\n\t\t\t\tfont-size: $uv-upload-message-font-size;\r\n\t\t\t\tcolor: $uv-upload-message-color;\r\n\t\t\t\tmargin-top: $uv-upload-message-margin-top;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__button {\r\n\t\t\t@include flex(column);\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\twidth: $uv-upload-button-width;\r\n\t\t\theight: $uv-upload-button-height;\r\n\t\t\tbackground-color: $uv-upload-button-bgColor;\r\n\t\t\tborder-radius: $uv-upload-button-border-radius;\r\n\t\t\tmargin: $uv-upload-botton-margin;\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tbox-sizing: border-box;\r\n\r\n\t\t\t/* #endif */\r\n\t\t\t&__text {\r\n\t\t\t\tfont-size: $uv-upload-text-font-size;\r\n\t\t\t\tcolor: $uv-upload-text-color;\r\n\t\t\t\tmargin-top: $uv-upload-text-margin-top;\r\n\t\t\t}\r\n\r\n\t\t\t&--hover {\r\n\t\t\t\tbackground-color: $uv-upload-hover-bgColor;\r\n\t\t\t}\r\n\r\n\t\t\t&--disabled {\r\n\t\t\t\topacity: $uv-upload-disabled-opacity;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-upload.vue?vue&type=style&index=0&id=2e4c50f9&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-upload.vue?vue&type=style&index=0&id=2e4c50f9&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759027614267\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}