<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 登录卡片 */
        .login-card {
            background: #ffffff;
            border-radius: 12px;
            padding: 40px 30px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            margin: 20px;
        }
        
        /* 头部区域 */
        .header-section {
            text-align: center;
            margin-bottom: 32px;
        }

        .title {
            font-size: 22px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }
        
        /* 表单区域 */
        .form-section {
            margin-bottom: 30px;
        }
        
        .input-group {
            margin-bottom: 20px;
            position: relative;
        }
        
        .input-field {
            width: 100%;
            padding: 16px 20px;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.2s ease;
            background: #f8f9fa;
        }

        .input-field:focus {
            border-color: #667eea;
        }

        .btn {
            width: 100%;
            padding: 14px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 12px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #07c160;
            color: white;
        }
        
        .btn-success:hover {
            background: #06ad56;
        }
        
        /* 分割线 */
        .divider {
            display: flex;
            align-items: center;
            margin: 30px 0;
        }
        
        .divider-line {
            flex: 1;
            height: 1px;
            background: #e0e0e0;
        }
        
        .divider-text {
            margin: 0 15px;
            font-size: 12px;
            color: #999;
        }
        
        /* 快捷获取手机号 */
        .quick-phone {
            text-align: center;
            margin-top: 15px;
        }
        
        .quick-phone-text {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .btn-small {
            padding: 8px 16px;
            font-size: 12px;
            background: transparent;
            color: #667eea;
            border: 1px solid #667eea;
        }
        
        /* 底部提示 */
        .footer {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 2;
            text-align: center;
        }
        
        .privacy-text {
            font-size: 11px;
            color: #999;
            line-height: 1.5;
        }

        .link-text {
            color: #667eea;
            text-decoration: none;
        }
        
        /* 响应式 */
        @media (max-height: 600px) {
            .login-card {
                padding: 30px 25px;
            }
            
            .header-section {
                margin-bottom: 30px;
            }
            
            .footer {
                bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="header-section">
            <div class="title">登录</div>
            <div class="subtitle">请输入手机号或使用微信快捷登录</div>
        </div>
        
        <div class="form-section">
            <div class="input-group">
                <input type="tel" class="input-field" placeholder="请输入手机号" maxlength="11">
            </div>
            
            <button class="btn btn-primary">手机号登录</button>
            
            <div class="divider">
                <div class="divider-line"></div>
                <div class="divider-text">或</div>
                <div class="divider-line"></div>
            </div>
            
            <button class="btn btn-success">🔗 微信一键登录</button>
            
            <div class="quick-phone">
                <div class="quick-phone-text">还没有手机号？</div>
                <button class="btn btn-small">微信授权获取</button>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <div class="privacy-text">
            登录即表示同意
            <span class="link-text">《用户协议》</span>
            和
            <span class="link-text">《隐私政策》</span>
        </div>
    </div>
</body>
</html>
