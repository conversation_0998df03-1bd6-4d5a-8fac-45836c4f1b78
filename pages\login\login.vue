<template>
	<view class="login-container">
		<!-- 登录卡片 -->
		<view class="login-card">
			<!-- Logo和标题 -->
			<view class="header-section">
				<view class="title">登录</view>
				<view class="subtitle">请输入手机号或使用微信快捷登录</view>
			</view>

			<!-- 表单区域 -->
			<view class="form-section">
				<u-form :model="form" ref="uForm" :rules="rules">
					<u-form-item prop="phone" borderBottom>
						<u-input
							v-model="form.phone"
							placeholder="请输入手机号"
							type="number"
							maxlength="11"
							prefixIcon="phone"
							clearable
							:customStyle="inputStyle"
							@focus="onInputFocus"
							@blur="onInputBlur"
						>
							<template #suffix>
								<u-button
									v-if="form.phone && !isValidPhone"
									text="格式错误"
									type="error"
									size="mini"
									disabled
								></u-button>
								<u-button
									v-else-if="form.phone && isValidPhone"
									text="格式正确"
									type="success"
									size="mini"
									disabled
								></u-button>
							</template>
						</u-input>
					</u-form-item>
				</u-form>

				<!-- 登录按钮 -->
				<view class="button-section">
					<u-button
						:text="loginButtonText"
						type="primary"
						size="large"
						:loading="isLoading"
						:disabled="!canLogin"
						@click="handlePhoneLogin"
						:customStyle="primaryButtonStyle"
					></u-button>
				</view>

				<!-- 分割线 -->
				<view class="divider-section">
					<view class="divider-line"></view>
					<view class="divider-text">或</view>
					<view class="divider-line"></view>
				</view>

				<!-- 微信登录区域 -->
				<view class="wechat-section">
					<!-- <u-button
						text="微信一键登录"
						type="success"
						size="large"
						open-type="getUserInfo"
						@getuserinfo="handleGetuserinfo"
						:customStyle="wechatButtonStyle"
						icon="weixin-fill"
					></u-button> -->

					<!-- 快捷获取手机号 -->
					<view class="quick-phone-section" v-if="!form.phone">
						<!-- <view class="quick-phone-text">还没有手机号？</view> -->
						<u-button
							text="微信授权获取"
							type="info"
							size="small"
							open-type="getPhoneNumber"
							@getphonenumber="getPhoneNumber"
							plain
						></u-button>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部提示 -->
		<view class="footer-section">
			<view class="privacy-text">
				登录即表示同意
				<text class="link-text">《用户协议》</text>
				和
				<text class="link-text">《隐私政策》</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				form: {
					phone: ''
				},
				isLoading: false,
				isInputFocused: false,
				rules: {
					phone: [
						{
							required: true,
							message: '请输入手机号',
							trigger: ['blur', 'change']
						},
						{
							pattern: /^1[3-9]\d{9}$/,
							message: '手机号格式不正确',
							trigger: ['blur', 'change']
						}
					]
				},
				inputStyle: {
					fontSize: '16px',
					padding: '16px 0',
					backgroundColor: '#f8f9fa',
					borderRadius: '8px'
				},
				primaryButtonStyle: {
					background: '#667eea',
					borderRadius: '8px',
					height: '48px',
					fontSize: '16px'
				},
				wechatButtonStyle: {
					borderRadius: '8px',
					height: '48px',
					fontSize: '16px'
				}
			};
		},
		computed: {
			isValidPhone() {
				return /^1[3-9]\d{9}$/.test(this.form.phone);
			},
			canLogin() {
				return this.form.phone && this.isValidPhone && !this.isLoading;
			},
			loginButtonText() {
				if (this.isLoading) return '登录中...';
				if (!this.form.phone) return '请输入手机号';
				if (!this.isValidPhone) return '手机号格式错误';
				return '手机号登录';
			}
		},
		methods: {
			// 手机号登录
			handlePhoneLogin() {
				if (!this.canLogin) return;

				this.$refs.uForm.validate().then(valid => {
					if (valid) {
						this.isLoading = true;
						this.wxlogin();
					}
				}).catch(errors => {
					console.log('表单验证失败：', errors);
				});
			},

			// 微信授权登录
			handleGetuserinfo(e) {
				console.log('微信用户信息：', e);
				this.isLoading = true;
				this.wxlogin();
			},

			// 获取微信手机号
			getPhoneNumber(e) {
				console.log('获取手机号：', e);
				const { code } = e.detail;
				if (code) {
					uni.showLoading({
						title: '获取中...'
					});
					this.$u.api.userPhone(code).then(res => {
						uni.hideLoading();
						this.form.phone = res.data.phoneNumber;
						this.$u.func.showToast({
							title: '手机号获取成功'
						});
					}).catch(() => {
						uni.hideLoading();
						this.$u.func.showToast({
							title: '获取手机号失败'
						});
					});
				} else {
					this.$u.func.showToast({
						title: '用户取消授权'
					});
				}
			},

			// 输入框聚焦事件
			onInputFocus() {
				this.isInputFocused = true;
			},

			// 输入框失焦事件
			onInputBlur() {
				this.isInputFocused = false;
			},

			// 微信登录逻辑
			wxlogin() {
				uni.login({
					complete: (res) => {
						this.$u.api
							.wxToken({
								code: res.code,
								phone: this.form.phone
							})
							.then(data => {
								this.$u.func.login(data);
								this.isLoading = false;
								this.$u.func.showToast({
									title: '登录成功',
									success: () => {
										setTimeout(() => {
											this.$u.func.redirect('/pages/index/index');
										}, 1000);
									}
								});
							})
							.catch(err => {
								this.isLoading = false;
								this.$u.func.showToast({
									title: err || '登录失败'
								});
							});
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.login-container {
		min-height: 100vh;
		background: #f8f9fa;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 40rpx;
		box-sizing: border-box;
	}

	// 登录卡片
	.login-card {
		background: #ffffff;
		border-radius: 16rpx;
		padding: 60rpx 40rpx;
		width: 100%;
		max-width: 600rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	}

	// 头部区域
	.header-section {
		text-align: center;
		margin-bottom: 50rpx;

		.title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 12rpx;
		}

		.subtitle {
			font-size: 26rpx;
			color: #666;
			line-height: 1.4;
		}
	}

	// 表单区域
	.form-section {
		.button-section {
			margin: 32rpx 0;
		}

		// 分割线
		.divider-section {
			display: flex;
			align-items: center;
			margin: 32rpx 0;

			.divider-line {
				flex: 1;
				height: 1rpx;
				background: #e5e5e5;
			}

			.divider-text {
				margin: 0 24rpx;
				font-size: 24rpx;
				color: #999;
			}
		}

		// 微信登录区域
		.wechat-section {
			.quick-phone-section {
				margin-top: 24rpx;
				text-align: center;

				.quick-phone-text {
					font-size: 24rpx;
					color: #666;
					margin-bottom: 12rpx;
				}
			}
		}
	}

	// 底部区域
	.footer-section {
		position: absolute;
		bottom: 60rpx;
		left: 50%;
		transform: translateX(-50%);

		.privacy-text {
			font-size: 22rpx;
			color: #999;
			text-align: center;
			line-height: 1.5;

			.link-text {
				color: #667eea;
				text-decoration: none;
			}
		}
	}

	// 响应式适配
	@media screen and (max-height: 800px) {
		.login-container {
			padding: 20rpx;
		}

		.login-card {
			padding: 40rpx 30rpx;
		}

		.footer-section {
			bottom: 30rpx;
		}
	}
</style>