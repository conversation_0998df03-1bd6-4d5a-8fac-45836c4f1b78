@charset "UTF-8";
/* 水平间距 */
/* 水平间距 */
view.data-v-50004b49, scroll-view.data-v-50004b49, swiper-item.data-v-50004b49 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-text.data-v-50004b49 {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;
  flex: 1;
  width: 100%;
}
.u-text__price.data-v-50004b49 {
  font-size: 14px;
  color: #606266;
}
.u-text__value.data-v-50004b49 {
  font-size: 14px;
  display: flex;
  flex-direction: row;
  color: #606266;
  flex-wrap: wrap;
  text-overflow: ellipsis;
  align-items: center;
}
.u-text__value--primary.data-v-50004b49 {
  color: #3c9cff;
}
.u-text__value--warning.data-v-50004b49 {
  color: #f9ae3d;
}
.u-text__value--success.data-v-50004b49 {
  color: #5ac725;
}
.u-text__value--info.data-v-50004b49 {
  color: #909399;
}
.u-text__value--error.data-v-50004b49 {
  color: #f56c6c;
}
.u-text__value--main.data-v-50004b49 {
  color: #303133;
}
.u-text__value--content.data-v-50004b49 {
  color: #606266;
}
.u-text__value--tips.data-v-50004b49 {
  color: #909193;
}
.u-text__value--light.data-v-50004b49 {
  color: #c0c4cc;
}

