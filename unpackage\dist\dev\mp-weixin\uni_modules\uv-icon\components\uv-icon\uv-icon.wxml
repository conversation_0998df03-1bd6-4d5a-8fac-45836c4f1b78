<view data-event-opts="{{[['tap',[['clickHandler',['$event']]]]]}}" class="{{['uv-icon','data-v-646dc59e','uv-icon--'+labelPos]}}" bindtap="__e"><block wx:if="{{isImg}}"><image class="uv-icon__img data-v-646dc59e" style="{{$root.s0}}" src="{{name}}" mode="{{imgMode}}"></image></block><block wx:else><text class="{{['uv-icon__icon','data-v-646dc59e',uClasses]}}" style="{{$root.s1}}" hover-class="{{hoverClass}}">{{icon}}</text></block><block wx:if="{{label!==''}}"><text class="uv-icon__label data-v-646dc59e" style="{{'color:'+(labelColor)+';'+('font-size:'+($root.g0)+';')+('margin-left:'+(labelPos=='right'?$root.g1:0)+';')+('margin-top:'+(labelPos=='bottom'?$root.g2:0)+';')+('margin-right:'+(labelPos=='left'?$root.g3:0)+';')+('margin-bottom:'+(labelPos=='top'?$root.g4:0)+';')}}">{{label}}</text></block></view>