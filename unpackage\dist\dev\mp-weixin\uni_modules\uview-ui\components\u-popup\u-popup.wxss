@charset "UTF-8";
/* 水平间距 */
/* 水平间距 */
view.data-v-3a231fda, scroll-view.data-v-3a231fda, swiper-item.data-v-3a231fda {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-popup.data-v-3a231fda {
  flex: 1;
}
.u-popup__content.data-v-3a231fda {
  background-color: #fff;
  position: relative;
}
.u-popup__content--round-top.data-v-3a231fda {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
.u-popup__content--round-left.data-v-3a231fda {
  border-top-left-radius: 0;
  border-top-right-radius: 10px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 10px;
}
.u-popup__content--round-right.data-v-3a231fda {
  border-top-left-radius: 10px;
  border-top-right-radius: 0;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 0;
}
.u-popup__content--round-bottom.data-v-3a231fda {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.u-popup__content--round-center.data-v-3a231fda {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
.u-popup__content__close.data-v-3a231fda {
  position: absolute;
}
.u-popup__content__close--hover.data-v-3a231fda {
  opacity: 0.4;
}
.u-popup__content__close--top-left.data-v-3a231fda {
  top: 15px;
  left: 15px;
}
.u-popup__content__close--top-right.data-v-3a231fda {
  top: 15px;
  right: 15px;
}
.u-popup__content__close--bottom-left.data-v-3a231fda {
  bottom: 15px;
  left: 15px;
}
.u-popup__content__close--bottom-right.data-v-3a231fda {
  right: 15px;
  bottom: 15px;
}

