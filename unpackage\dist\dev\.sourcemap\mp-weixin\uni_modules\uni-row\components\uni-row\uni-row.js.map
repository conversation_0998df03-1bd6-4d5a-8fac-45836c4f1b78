{"version": 3, "sources": ["webpack:///D:/project/vt-unih5-order/uni_modules/uni-row/components/uni-row/uni-row.vue?c595", "webpack:///D:/project/vt-unih5-order/uni_modules/uni-row/components/uni-row/uni-row.vue?0339", "webpack:///D:/project/vt-unih5-order/uni_modules/uni-row/components/uni-row/uni-row.vue?3081", "webpack:///D:/project/vt-unih5-order/uni_modules/uni-row/components/uni-row/uni-row.vue?d926", "uni-app:///uni_modules/uni-row/components/uni-row/uni-row.vue", "webpack:///D:/project/vt-unih5-order/uni_modules/uni-row/components/uni-row/uni-row.vue?8844", "webpack:///D:/project/vt-unih5-order/uni_modules/uni-row/components/uni-row/uni-row.vue?209a"], "names": ["name", "componentName", "options", "virtualHost", "props", "type", "gutter", "justify", "default", "align", "width", "created", "computed", "marginValue", "typeClass", "justifyClass", "alignClass"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAkoB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACUtpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,eAiBA;EACAA;EACAC;EAEAC;IACAC;EACA;;EAEAC;IACAC;IACAC;IACAC;MACAF;MACAG;IACA;IACAC;MACAJ;MACAG;IACA;IACA;IACAE;MACAL;MACAG;IACA;EACA;EACAG,6BAIA;EACAC;IACAC;MAEA;QACA;MACA;MAEA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAAqsC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACAztC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-row/components/uni-row/uni-row.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-row.vue?vue&type=template&id=1d993189&\"\nvar renderjs\nimport script from \"./uni-row.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-row.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-row.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-row/components/uni-row/uni-row.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-row.vue?vue&type=template&id=1d993189&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = Number(_vm.marginValue)\n  var m1 = Number(_vm.marginValue)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-row.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-row.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :class=\"[ 'uni-row', typeClass , justifyClass, alignClass, ]\" :style=\"{\r\n\t\tmarginLeft:`${Number(marginValue)}rpx`,\r\n\t\tmarginRight:`${Number(marginValue)}rpx`,\r\n\t}\">\r\n\t\t<slot></slot>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst ComponentClass = 'uni-row';\r\n\tconst modifierSeparator = '--';\r\n\t/**\r\n\t * Row\t布局-行\r\n\t * @description\t流式栅格系统，随着屏幕或视口分为 24 份，可以迅速简便地创建布局。\r\n\t * @tutorial\thttps://ext.dcloud.net.cn/plugin?id=3958\r\n\t *\r\n\t * @property\t{gutter} type = Number 栅格间隔\r\n\t * @property\t{justify} type = String flex 布局下的水平排列方式\r\n\t * \t\t\t\t\t\t可选\tstart/end/center/space-around/space-between\tstart\r\n\t * \t\t\t\t\t\t默认值\tstart\r\n\t * @property\t{align} type = String flex 布局下的垂直排列方式\r\n\t * \t\t\t\t\t\t可选\ttop/middle/bottom\r\n\t * \t\t\t\t\t\t默认值\ttop\r\n\t * @property\t{width} type = String|Number nvue下需要自行配置宽度用于计算\r\n\t * \t\t\t\t\t\t默认值 750\r\n\t */\r\n\r\n\r\n\texport default {\r\n\t\tname: 'uniRow',\r\n\t\tcomponentName: 'uniRow',\r\n\t\t// #ifdef MP-WEIXIN\r\n\t\toptions: {\r\n\t\t\tvirtualHost: true // 在微信小程序中将组件节点渲染为虚拟节点，更加接近Vue组件的表现，可使用flex布局\r\n\t\t},\r\n\t\t// #endif\r\n\t\tprops: {\r\n\t\t\ttype: String,\r\n\t\t\tgutter: Number,\r\n\t\t\tjustify: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'start'\r\n\t\t\t},\r\n\t\t\talign: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'top'\r\n\t\t\t},\r\n\t\t\t// nvue如果使用span等属性，需要配置宽度\r\n\t\t\twidth: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 750\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tthis.type = 'flex';\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tmarginValue() {\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tif (this.gutter) {\r\n\t\t\t\t\treturn -(this.gutter / 2);\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn 0;\r\n\t\t\t},\r\n\t\t\ttypeClass() {\r\n\t\t\t\treturn this.type === 'flex' ? `${ComponentClass + modifierSeparator}flex` : '';\r\n\t\t\t},\r\n\t\t\tjustifyClass() {\r\n\t\t\t\treturn this.justify !== 'start' ? `${ComponentClass + modifierSeparator}flex-justify-${this.justify}` : ''\r\n\t\t\t},\r\n\t\t\talignClass() {\r\n\t\t\t\treturn this.align !== 'top' ? `${ComponentClass + modifierSeparator}flex-align-${this.align}` : ''\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t$layout-namespace: \".uni-\";\r\n\t$row:$layout-namespace+\"row\";\r\n\t$modifier-separator: \"--\";\r\n\r\n\t@mixin utils-clearfix {\r\n\t\t$selector: &;\r\n\r\n\t\t@at-root {\r\n\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t#{$selector}::before,\r\n\t\t\t#{$selector}::after {\r\n\t\t\t\tdisplay: table;\r\n\t\t\t\tcontent: \"\";\r\n\t\t\t}\r\n\r\n\t\t\t#{$selector}::after {\r\n\t\t\t\tclear: both;\r\n\t\t\t}\r\n\r\n\t\t\t/* #endif */\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t@mixin utils-flex ($direction: row) {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: $direction;\r\n\t}\r\n\r\n\t@mixin set-flex($state) {\r\n\t\t@at-root &-#{$state} {\r\n\t\t\t@content\r\n\t\t}\r\n\t}\r\n\r\n\t#{$row} {\r\n\t\tposition: relative;\r\n\t\tflex-direction: row;\r\n\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tflex: 1;\r\n\t\t/* #endif */\r\n\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tbox-sizing: border-box;\r\n\t\t/* #endif */\r\n\r\n\t\t// 非nvue使用float布局\r\n\t\t@include utils-clearfix;\r\n\r\n\t\t// 在QQ、字节、百度小程序平台，编译后使用shadow dom，不可使用flex布局，使用float\r\n\t\t@at-root {\r\n\r\n\t\t\t/* #ifndef MP-QQ || MP-TOUTIAO || MP-BAIDU */\r\n\t\t\t&#{$modifier-separator}flex {\r\n\t\t\t\t@include utils-flex;\r\n\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\tflex: 1;\r\n\r\n\t\t\t\t&:before,\r\n\t\t\t\t&:after {\r\n\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t}\r\n\r\n\t\t\t\t@include set-flex(justify-center) {\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t@include set-flex(justify-end) {\r\n\t\t\t\t\tjustify-content: flex-end;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t@include set-flex(justify-space-between) {\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t@include set-flex(justify-space-around) {\r\n\t\t\t\t\tjustify-content: space-around;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t@include set-flex(align-middle) {\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t@include set-flex(align-bottom) {\r\n\t\t\t\t\talign-items: flex-end;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t/* #endif */\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t// 字节、QQ配置后不生效\r\n\t// 此处用法无法使用scoped\r\n\t/* #ifdef MP-WEIXIN || MP-TOUTIAO || MP-QQ */\r\n\t:host {\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t/* #endif */\r\n</style>\r\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-row.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-row.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759027613916\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}