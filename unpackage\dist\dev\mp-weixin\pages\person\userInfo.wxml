<view style="padding:20rpx;box-sizing:border-box;" class="data-v-327543aa"><u-cell-group vue-id="1c33b29c-1" class="data-v-327543aa" bind:__l="__l" vue-slots="{{['default']}}"><u-cell vue-id="{{('1c33b29c-2')+','+('1c33b29c-1')}}" title="头像" class="data-v-327543aa" bind:__l="__l" vue-slots="{{['value']}}"><button style="display:flex;align-items:center;justify-content:center;width:70rpx;height:70rpx;padding:5rpx;" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['onChooseAvatar',['$event']]]]]}}" bindchooseavatar="__e" class="data-v-327543aa" slot="value"><image style="width:70rpx;height:70rpx;" src="{{userInfo.avatar}}" class="data-v-327543aa"></image></button></u-cell><u-cell vue-id="{{('1c33b29c-3')+','+('1c33b29c-1')}}" title="昵称" value="" class="data-v-327543aa" bind:__l="__l" vue-slots="{{['value']}}"><u-input bind:input="__e" vue-id="{{('1c33b29c-4')+','+('1c33b29c-3')}}" placeholder="请输入昵称" type="nickname" border="{{true}}" value="{{userInfo.name}}" data-event-opts="{{[['^input',[['__set_model',['$0','name','$event',[]],['userInfo']]]]]}}" class="data-v-327543aa" slot="value" bind:__l="__l"></u-input></u-cell><u-cell vue-id="{{('1c33b29c-5')+','+('1c33b29c-1')}}" title="真实姓名" value="" class="data-v-327543aa" bind:__l="__l" vue-slots="{{['value']}}"><u-input bind:input="__e" vue-id="{{('1c33b29c-6')+','+('1c33b29c-5')}}" placeholder="请输入姓名" type="nickname" border="{{true}}" value="{{userInfo.realName}}" data-event-opts="{{[['^input',[['__set_model',['$0','realName','$event',[]],['userInfo']]]]]}}" class="data-v-327543aa" slot="value" bind:__l="__l"></u-input></u-cell><u-cell vue-id="{{('1c33b29c-7')+','+('1c33b29c-1')}}" title="手机号" value="" class="data-v-327543aa" bind:__l="__l" vue-slots="{{['value']}}"><u-input bind:input="__e" style="width:300rpx;" vue-id="{{('1c33b29c-8')+','+('1c33b29c-7')}}" readonly="{{true}}" placeholder="请输入手机号" type="number" border="{{true}}" value="{{userInfo.phone}}" data-event-opts="{{[['^input',[['__set_model',['$0','phone','$event',[]],['userInfo']]]]]}}" class="data-v-327543aa" slot="value" bind:__l="__l" vue-slots="{{['suffix']}}"><view slot="suffix" class="data-v-327543aa"></view></u-input></u-cell></u-cell-group><view style="margin-top:20rpx;" class="data-v-327543aa"><u-button vue-id="1c33b29c-9" size="large" type="primary" text="保存" data-event-opts="{{[['^click',[['handleSave']]]]}}" bind:click="__e" class="data-v-327543aa" bind:__l="__l"></u-button></view></view>