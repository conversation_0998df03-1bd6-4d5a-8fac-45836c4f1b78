import http from '@/http/api.js'


// 获取用户信息
const userInfo = () => {
	return http.request({
		url: '/blade-system/user/info',
		method: 'GET',
	})
}
// 更改用户信息
const updateUser = (data) => {
	return http.request({
		url: '/blade-system/user/update-info',
		method: 'post',
		data
	})
}

// 获取用户手机号
const userPhone = (code) => {
	return http.request({
		url: '/blade-system/wechat_mini/getPhoneByPortalMini',
		method: 'GET',
		params: {
			code
		}
	})
}
// 获取公司信息
export const getCompanyDetail = () => {
	return http.request({
		url: '/blade-system/tenant/detail',
		method: 'get',
		params: {
			tenantId: '806174'
		}
	})
}
export default {

	userInfo,
	updateUser,
	userPhone,
	getCompanyDetail
}