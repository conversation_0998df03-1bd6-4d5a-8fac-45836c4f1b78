{"version": 3, "sources": ["webpack:///D:/project/vt-unih5-order/uni_modules/uni-section/components/uni-section/uni-section.vue?4fbb", "webpack:///D:/project/vt-unih5-order/uni_modules/uni-section/components/uni-section/uni-section.vue?9bf6", "webpack:///D:/project/vt-unih5-order/uni_modules/uni-section/components/uni-section/uni-section.vue?6423", "webpack:///D:/project/vt-unih5-order/uni_modules/uni-section/components/uni-section/uni-section.vue?cec9", "uni-app:///uni_modules/uni-section/components/uni-section/uni-section.vue", "webpack:///D:/project/vt-unih5-order/uni_modules/uni-section/components/uni-section/uni-section.vue?17b2", "webpack:///D:/project/vt-unih5-order/uni_modules/uni-section/components/uni-section/uni-section.vue?77b7"], "names": ["name", "emits", "props", "type", "default", "title", "required", "titleFontSize", "titleColor", "subTitle", "subTitleFontSize", "subTitleColor", "padding", "computed", "_padding", "watch", "uni", "methods", "onClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAsoB,CAAgB,qnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwB1pB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,eAgBA;EACAA;EACAC;EACAC;IACAC;MACAA;MACAC;IACA;IACAC;MACAF;MACAG;MACAF;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;EACA;EACAS;IACAC;MACA;QACA;MACA;MAEA;IACA;EACA;EACAC;IACAV;MACA;QACAW;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnGA;AAAA;AAAA;AAAA;AAAysC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACA7tC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-section/components/uni-section/uni-section.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-section.vue?vue&type=template&id=f7ca1098&\"\nvar renderjs\nimport script from \"./uni-section.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-section.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-section.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-section/components/uni-section/uni-section.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-section.vue?vue&type=template&id=f7ca1098&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-section.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-section.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-section\">\r\n\t\t<view class=\"uni-section-header\" @click=\"onClick\">\r\n\t\t\t\t<view class=\"uni-section-header__decoration\" v-if=\"type\" :class=\"type\" />\r\n        <slot v-else name=\"decoration\"></slot>\r\n\r\n        <view class=\"uni-section-header__content\">\r\n          <text :style=\"{'font-size':titleFontSize,'color':titleColor}\" class=\"uni-section__content-title\" :class=\"{'distraction':!subTitle}\">{{ title }}</text>\r\n          <text v-if=\"subTitle\" :style=\"{'font-size':subTitleFontSize,'color':subTitleColor}\" class=\"uni-section-header__content-sub\">{{ subTitle }}</text>\r\n        </view>\r\n\r\n        <view class=\"uni-section-header__slot-right\">\r\n          <slot name=\"right\"></slot>\r\n        </view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"uni-section-content\" :style=\"{padding: _padding}\">\r\n\t\t\t<slot />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\r\n\t/**\r\n\t * Section 标题栏\r\n\t * @description 标题栏\r\n\t * @property {String} type = [line|circle|square] 标题装饰类型\r\n\t * \t@value line 竖线\r\n\t * \t@value circle 圆形\r\n\t * \t@value square 正方形\r\n\t * @property {String} title 主标题\r\n\t * @property {String} titleFontSize 主标题字体大小\r\n\t * @property {String} titleColor 主标题字体颜色\r\n\t * @property {String} subTitle 副标题\r\n\t * @property {String} subTitleFontSize 副标题字体大小\r\n\t * @property {String} subTitleColor 副标题字体颜色\r\n\t * @property {String} padding 默认插槽 padding\r\n\t */\r\n\r\n\texport default {\r\n\t\tname: 'UniSection',\r\n    emits:['click'],\r\n\t\tprops: {\r\n\t\t\ttype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\trequired: true,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n      titleFontSize: {\r\n        type: String,\r\n        default: '14px'\r\n      },\r\n\t\t\ttitleColor:{\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#333'\r\n\t\t\t},\r\n\t\t\tsubTitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n      subTitleFontSize: {\r\n        type: String,\r\n        default: '12px'\r\n      },\r\n      subTitleColor: {\r\n        type: String,\r\n        default: '#999'\r\n      },\r\n\t\t\tpadding: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n    computed:{\r\n      _padding(){\r\n        if(typeof this.padding === 'string'){\r\n          return this.padding\r\n        }\r\n\r\n        return this.padding?'10px':''\r\n      }\r\n    },\r\n\t\twatch: {\r\n\t\t\ttitle(newVal) {\r\n\t\t\t\tif (uni.report && newVal !== '') {\r\n\t\t\t\t\tuni.report('title', newVal)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n    methods: {\r\n\t\t\tonClick() {\r\n\t\t\t\tthis.$emit('click')\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\" >\r\n\t$uni-primary: #2979ff !default;\r\n\r\n\t.uni-section {\r\n\t\tbackground-color: #fff;\r\n    .uni-section-header {\r\n      position: relative;\r\n      /* #ifndef APP-NVUE */\r\n      display: flex;\r\n      /* #endif */\r\n      flex-direction: row;\r\n      align-items: center;\r\n      padding: 12px 10px;\r\n      font-weight: normal;\r\n\r\n      &__decoration{\r\n        margin-right: 6px;\r\n        background-color: $uni-primary;\r\n        &.line {\r\n          width: 4px;\r\n          height: 12px;\r\n          border-radius: 10px;\r\n        }\r\n\r\n        &.circle {\r\n          width: 8px;\r\n          height: 8px;\r\n          border-top-right-radius: 50px;\r\n          border-top-left-radius: 50px;\r\n          border-bottom-left-radius: 50px;\r\n          border-bottom-right-radius: 50px;\r\n        }\r\n\r\n        &.square {\r\n          width: 8px;\r\n          height: 8px;\r\n        }\r\n      }\r\n\r\n      &__content {\r\n        /* #ifndef APP-NVUE */\r\n        display: flex;\r\n        /* #endif */\r\n        flex-direction: column;\r\n        flex: 1;\r\n        color: #333;\r\n\r\n        .distraction {\r\n          flex-direction: row;\r\n          align-items: center;\r\n        }\r\n        &-sub {\r\n          margin-top: 2px;\r\n        }\r\n      }\r\n\r\n      &__slot-right{\r\n        font-size: 14px;\r\n      }\r\n    }\r\n\r\n    .uni-section-content{\r\n      font-size: 14px;\r\n    }\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-section.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-section.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759027613898\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}