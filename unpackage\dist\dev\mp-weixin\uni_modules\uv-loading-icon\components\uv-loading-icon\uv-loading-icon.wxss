@charset "UTF-8";
/* 水平间距 */
/* 水平间距 */
view.data-v-41b06902, scroll-view.data-v-41b06902, swiper-item.data-v-41b06902 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-loading-icon.data-v-41b06902 {
  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: #c8c9cc;
}
.uv-loading-icon__text.data-v-41b06902 {
  margin-left: 4px;
  color: #606266;
  font-size: 14px;
  line-height: 20px;
}
.uv-loading-icon__spinner.data-v-41b06902 {
  width: 30px;
  height: 30px;
  position: relative;
  box-sizing: border-box;
  max-width: 100%;
  max-height: 100%;
  -webkit-animation: uv-rotate-data-v-41b06902 1s linear infinite;
          animation: uv-rotate-data-v-41b06902 1s linear infinite;
}
.uv-loading-icon__spinner--semicircle.data-v-41b06902 {
  border-width: 2px;
  border-color: transparent;
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
  border-style: solid;
}
.uv-loading-icon__spinner--circle.data-v-41b06902 {
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
  border-width: 2px;
  border-top-color: #e5e5e5;
  border-right-color: #e5e5e5;
  border-bottom-color: #e5e5e5;
  border-left-color: #e5e5e5;
  border-style: solid;
}
.uv-loading-icon--vertical.data-v-41b06902 {
  flex-direction: column;
}
.data-v-41b06902:host {
  font-size: 0px;
  line-height: 1;
}
.uv-loading-icon__spinner--spinner.data-v-41b06902 {
  -webkit-animation-timing-function: steps(12);
          animation-timing-function: steps(12);
}
.uv-loading-icon__text.data-v-41b06902:empty {
  display: none;
}
.uv-loading-icon--vertical .uv-loading-icon__text.data-v-41b06902 {
  margin: 6px 0 0;
  color: #606266;
}
.uv-loading-icon__dot.data-v-41b06902 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.uv-loading-icon__dot.data-v-41b06902:before {
  display: block;
  width: 2px;
  height: 25%;
  margin: 0 auto;
  background-color: currentColor;
  border-radius: 40%;
  content: " ";
}
.uv-loading-icon__dot.data-v-41b06902:nth-of-type(1) {
  -webkit-transform: rotate(30deg);
          transform: rotate(30deg);
  opacity: 1;
}
.uv-loading-icon__dot.data-v-41b06902:nth-of-type(2) {
  -webkit-transform: rotate(60deg);
          transform: rotate(60deg);
  opacity: 0.9375;
}
.uv-loading-icon__dot.data-v-41b06902:nth-of-type(3) {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
  opacity: 0.875;
}
.uv-loading-icon__dot.data-v-41b06902:nth-of-type(4) {
  -webkit-transform: rotate(120deg);
          transform: rotate(120deg);
  opacity: 0.8125;
}
.uv-loading-icon__dot.data-v-41b06902:nth-of-type(5) {
  -webkit-transform: rotate(150deg);
          transform: rotate(150deg);
  opacity: 0.75;
}
.uv-loading-icon__dot.data-v-41b06902:nth-of-type(6) {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
  opacity: 0.6875;
}
.uv-loading-icon__dot.data-v-41b06902:nth-of-type(7) {
  -webkit-transform: rotate(210deg);
          transform: rotate(210deg);
  opacity: 0.625;
}
.uv-loading-icon__dot.data-v-41b06902:nth-of-type(8) {
  -webkit-transform: rotate(240deg);
          transform: rotate(240deg);
  opacity: 0.5625;
}
.uv-loading-icon__dot.data-v-41b06902:nth-of-type(9) {
  -webkit-transform: rotate(270deg);
          transform: rotate(270deg);
  opacity: 0.5;
}
.uv-loading-icon__dot.data-v-41b06902:nth-of-type(10) {
  -webkit-transform: rotate(300deg);
          transform: rotate(300deg);
  opacity: 0.4375;
}
.uv-loading-icon__dot.data-v-41b06902:nth-of-type(11) {
  -webkit-transform: rotate(330deg);
          transform: rotate(330deg);
  opacity: 0.375;
}
.uv-loading-icon__dot.data-v-41b06902:nth-of-type(12) {
  -webkit-transform: rotate(360deg);
          transform: rotate(360deg);
  opacity: 0.3125;
}
@-webkit-keyframes uv-rotate-data-v-41b06902 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(1turn);
            transform: rotate(1turn);
}
}
@keyframes uv-rotate-data-v-41b06902 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(1turn);
            transform: rotate(1turn);
}
}

