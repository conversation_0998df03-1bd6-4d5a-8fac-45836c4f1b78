@charset "UTF-8";
/* 水平间距 */
/* 水平间距 */
.login-container.data-v-b237504c {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  box-sizing: border-box;
}
.login-card.data-v-b237504c {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 60rpx 40rpx;
  width: 100%;
  max-width: 600rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.header-section.data-v-b237504c {
  text-align: center;
  margin-bottom: 50rpx;
}
.header-section .title.data-v-b237504c {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}
.header-section .subtitle.data-v-b237504c {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}
.form-section .button-section.data-v-b237504c {
  margin: 32rpx 0;
}
.form-section .divider-section.data-v-b237504c {
  display: flex;
  align-items: center;
  margin: 32rpx 0;
}
.form-section .divider-section .divider-line.data-v-b237504c {
  flex: 1;
  height: 1rpx;
  background: #e5e5e5;
}
.form-section .divider-section .divider-text.data-v-b237504c {
  margin: 0 24rpx;
  font-size: 24rpx;
  color: #999;
}
.form-section .wechat-section .quick-phone-section.data-v-b237504c {
  margin-top: 24rpx;
  text-align: center;
}
.form-section .wechat-section .quick-phone-section .quick-phone-text.data-v-b237504c {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}
.footer-section.data-v-b237504c {
  position: absolute;
  bottom: 60rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.footer-section .privacy-text.data-v-b237504c {
  font-size: 22rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}
.footer-section .privacy-text .link-text.data-v-b237504c {
  color: #667eea;
  text-decoration: none;
}
@media screen and (max-height: 800px) {
.login-container.data-v-b237504c {
    padding: 20rpx;
}
.login-card.data-v-b237504c {
    padding: 40rpx 30rpx;
}
.footer-section.data-v-b237504c {
    bottom: 30rpx;
}
}

